import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BigNumber from "bignumber.js";
import _, { first } from "lodash";
import React, {
  createContext,
  memo,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { EDex } from "@/enums";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import { toastError, toastSuccess } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { AppDispatch, RootState } from "@/store";
import { TPrice } from "@/store/metadata.store";
import {
  getAllWalletAndBalanceUser,
  removePosition,
  setBalance,
  // setBalance,
  setOpenOrders,
  setPosition,
  setWallets,
  setWalletTracker,
} from "@/store/user.store";
import {
  TPairTransaction,
  TPosition,
  TPositionUpdatedSocket,
  TWallet,
  TWsOrder,
} from "@/types";
import {
  NETWORKS,
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
} from "@/utils/contants";
import {
  convertMistToDec,
  dividedBN,
  getLinkTxExplorer,
  isZero,
  multipliedBN,
  sleep,
} from "@/utils/helper";
import { simulateSellExactIn, simulateSellPosition } from "@/utils/simulates";
import {
  getCoinBalanceOnchain,
  getOwnerCoinsOnchain,
  getReferenceGasPrice,
} from "@/utils/suiClient";
import { TGroup, TWalletTracker } from "../types/wallet-tracker";
import { toast } from "react-toastify";
import ToastNotification from "../components/WalletTracker/ToastNotification";

type WalletContextType = {
  positions?: TPosition[];
};

const WalletContext = createContext<WalletContextType>({});

const WalletDataHandler = memo(() => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const walletsRef = useRef<TWallet[]>([]);

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    walletsRef.current = wallets;
  }, [wallets]);

  const handleUpdateUserBalance = async (event: TBroadcastEvent) => {
    const data = event.detail;
    const updatedWallets =
      walletsRef.current?.map((wallet) => {
        if (
          normalizeSuiAddress(wallet.address) ===
            normalizeSuiAddress(data.walletAddress) &&
          [SUI_TOKEN_ADDRESS_FULL, SUI_TOKEN_ADDRESS_SHORT]?.includes(
            normalizeStructTag(data.token.address)
          )
        ) {
          return {
            ...wallet,
            balance: data.balance,
          };
        }
        return wallet;
      }) || [];
    walletsRef.current = updatedWallets;
    dispatch(setWallets({ wallets: updatedWallets }));
    dispatch(setBalance(data));
  };

  useEffect(() => {
    if (accessToken) {
      dispatch(
        getAllWalletAndBalanceUser({ network: NETWORKS.SUI, isFirstTime: true })
      );
    }
    if (!accessToken || !connectedSocket) return;
    AppBroadcast.on(BROADCAST_EVENTS.WALLET_UPDATED, handleUpdateUserBalance);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.WALLET_UPDATED,
        handleUpdateUserBalance
      );
    };
  }, [accessToken, connectedSocket]);

  return null;
});

WalletDataHandler.displayName = "WalletDataHandler";

const PERSIST_POSITION_KEY = "raidenx-persist-positions-v0.0.5";
const PERSIST_POSITION_TIME_VALID = 60 * 1000; // 5 minutes

const PersistPositionDataHandler = () => {
  const positions = useSelector((state: RootState) => state.user.positions);
  useEffect(() => {
    const filteredPositions = positions.filter(
      (position) => !!position.simulateAt
    );
    if (filteredPositions.length) {
      sessionStorage.setItem(
        PERSIST_POSITION_KEY,
        JSON.stringify(filteredPositions)
      );
    }
  }, [positions]);
  return null;
};

// Extended interface for position with balanceUpdateAt
interface TPositionWithUpdateTime extends TPosition {
  balanceUpdateAt?: number;
}

const PositionDataHandler = memo(() => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const suiPriceUsd = useSelector(
    (state: RootState) => state.metadata.suiPriceUsd
  );
  const quotePrices = useSelector(
    (state: RootState) => state.metadata.quotePrices
  );
  const positionsRef = useRef<TPositionWithUpdateTime[]>([]);
  const suiPriceUsdRef = useRef("0");
  const quotePricesRef = useRef<{ [key: string]: TPrice }>({});
  const debouncedPnLPositionNeedUpdate = useRef(new Set());
  const balanceUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    suiPriceUsdRef.current = suiPriceUsd;
  }, [suiPriceUsd]);

  useEffect(() => {
    quotePricesRef.current = quotePrices;
  }, [quotePrices]);

  // Function to update balance for positions that need updating
  const updatePositionsBalance = async () => {
    const currentTime = Date.now();
    const positionsToUpdate = positionsRef.current.filter(
      (position) =>
        position.balanceUpdateAt && position.balanceUpdateAt <= currentTime
    );

    if (positionsToUpdate.length === 0) return;

    // Update balance for each position
    const updatedPositions = [...positionsRef.current];
    let hasChanges = false;

    for (const position of positionsToUpdate) {
      try {
        const currentBalanceOnchain = await getCoinBalanceOnchain(
          position.walletName,
          position.token.address
        );

        const currentBalance = convertMistToDec(
          currentBalanceOnchain,
          position.token.decimals
        );

        const positionIndex = updatedPositions.findIndex(
          (p) =>
            p.pair === position.pair && p.walletName === position.walletName
        );

        if (positionIndex !== -1) {
          if (isZero(currentBalance)) {
            // Remove position if balance = 0
            dispatch(removePosition(updatedPositions[positionIndex]));
            updatedPositions.splice(positionIndex, 1);
            hasChanges = true;
          } else {
            // Update balance and recalculate PnL
            updatedPositions[positionIndex] = {
              ...updatedPositions[positionIndex],
              balance: currentBalance,
              balanceUpdateAt: undefined, // Clear balanceUpdateAt after update
            };

            // Recalculate PnL
            const updated = await attachEstimateOut(
              updatedPositions[positionIndex]
            );
            updatedPositions[positionIndex] = updated;
            dispatch(setPosition(updated));
            hasChanges = true;
          }
        }
      } catch (error) {
        console.error(
          "Error updating balance for position:",
          position.pair,
          error
        );
      }
    }

    if (hasChanges) {
      positionsRef.current = updatedPositions;
    }
  };

  // Setup interval to update balance periodically
  useEffect(() => {
    if (!accessToken || !connectedSocket) return;

    // Run interval every 500ms
    balanceUpdateIntervalRef.current = setInterval(updatePositionsBalance, 500);

    return () => {
      if (balanceUpdateIntervalRef.current) {
        clearInterval(balanceUpdateIntervalRef.current);
        balanceUpdateIntervalRef.current = null;
      }
    };
  }, [accessToken, connectedSocket]);

  useEffect(() => {
    if (accessToken) {
      getUserPositions();
    }
    if (!accessToken || !connectedSocket) return;
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, getUserPositions);
    AppBroadcast.on(BROADCAST_EVENTS.SHOW_POSITION, handleShowPosition);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, getUserPositions);
      AppBroadcast.remove(BROADCAST_EVENTS.SHOW_POSITION, handleShowPosition);
    };
  }, [accessToken, connectedSocket]);

  const handleShowPosition = async (event: TBroadcastEvent) => {
    const tokenAddress = event?.detail;
    if (!tokenAddress) {
      return;
    }

    const res = await rf
      .getRequest("MyPositionRequest")
      .getMyPositionByTokenAddress(NETWORKS.SUI, tokenAddress);
    const positionDetail = first(res?.docs);
    if (!!positionDetail) {
      (positionsRef.current || [])?.push(
        positionDetail as TPositionWithUpdateTime
      );
      dispatch(setPosition(positionDetail));
    }
  };

  const getUserPositions = async () => {
    const limit = 100;
    let page = 1;
    let allDocs: any[] = [];
    let hasNextPage = true;

    while (hasNextPage) {
      const res = await rf
        .getRequest("MyPositionRequest")
        .getMyPositions(NETWORKS.SUI, {
          page,
          limit,
          isHidden: false,
        });

      const dataPosition = res.docs.filter(
        (item: any) =>
          item?.token?.address &&
          normalizeStructTag(item?.token?.address) !==
            normalizeStructTag(SUI_TOKEN_ADDRESS_FULL)
      );

      allDocs = [...allDocs, ...dataPosition];

      if (!dataPosition?.length) {
        hasNextPage = false;
      } else {
        page++;
      }
    }
    positionsRef.current = (allDocs || []) as TPositionWithUpdateTime[];
    const positions = positionsRef.current || [];
    positions.map((position) => dispatch(setPosition(position)));

    // simulate sell position
    positionsRef.current = await calculatePnLManyPosition(positions);
    positionsRef.current.map((position) => {
      subscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.PAIR_DETAIL(position.pair)
      );
    });
  };

  const calculatePnLManyPosition = async (
    positions: TPositionWithUpdateTime[]
  ): Promise<TPositionWithUpdateTime[]> => {
    const uniqueWalletAddresses = _.uniq(
      positions.map((position) => position.walletName)
    );
    let userAllCoins: any = [];
    for (const walletAddress of uniqueWalletAddresses) {
      const coins = await getOwnerCoinsOnchain(walletAddress);
      userAllCoins = userAllCoins.concat(coins);
    }
    const gasBasePrice = await getReferenceGasPrice();

    const persistPositions = JSON.parse(
      sessionStorage.getItem(PERSIST_POSITION_KEY) || "[]"
    );
    const isSamePosition = (
      p: TPositionWithUpdateTime,
      position: TPositionWithUpdateTime
    ) =>
      p.pair === position.pair &&
      p.poolId === position.poolId &&
      p.dex === position.dex &&
      normalizeStructTag(p.token.address) ===
        normalizeStructTag(position.token.address) &&
      normalizeSuiAddress(p.walletName) ===
        normalizeSuiAddress(position.walletName) &&
      new BigNumber(p.balance).comparedTo(new BigNumber(position.balance)) ===
        0 &&
      new BigNumber(p.volumeUsdBought).comparedTo(
        new BigNumber(position.volumeUsdBought)
      ) === 0 &&
      new BigNumber(p.volumeUsdSold).comparedTo(
        new BigNumber(position.volumeUsdSold)
      ) === 0;

    const isPositionCacheValid = (p: TPositionWithUpdateTime) =>
      Number(p.simulateAt || 0) >=
      new Date().valueOf() - PERSIST_POSITION_TIME_VALID;

    const getCachedPosition = (position: TPositionWithUpdateTime) => {
      const cachedPosition = persistPositions.find(
        (p: TPositionWithUpdateTime) =>
          isSamePosition(p, position) && isPositionCacheValid(p)
      );
      return cachedPosition;
    };

    let updatedPositions: TPositionWithUpdateTime[] = [];
    const batches = _.chunk(positions, 5);
    for (const batch of batches) {
      let batchResults = await Promise.all(
        batch.map(async (position) => {
          const cachedPosition = getCachedPosition(position);
          return (
            cachedPosition ||
            ((await simulateSellPosition(
              position,
              userAllCoins,
              gasBasePrice,
              quotePricesRef.current[
                position?.tokenQuote?.address || SUI_TOKEN_ADDRESS_FULL
              ]?.priceUsd
            )) as TPositionWithUpdateTime)
          );
        })
      );
      batchResults = batchResults.map((position) => {
        const usdBalance = multipliedBN(
          position.balanceToQuote || 0,
          quotePricesRef.current[
            position?.tokenQuote?.address || SUI_TOKEN_ADDRESS_FULL
          ]?.priceUsd
        );
        return calcPositionPnL({
          ...position,
          balanceToUsd: usdBalance,
          balanceToSui: dividedBN(usdBalance, suiPriceUsdRef.current),
        });
      });
      batchResults.map((position) => {
        dispatch(setPosition(position));
      });
      updatedPositions = [...updatedPositions, ...batchResults];
      await sleep(500);
    }
    return updatedPositions;
  };

  const attachEstimateOut = async (
    position: TPositionWithUpdateTime
  ): Promise<TPositionWithUpdateTime> => {
    let tokenOut = "0";
    let usdBalance = "0";
    try {
      tokenOut = await simulateSellExactIn(
        position.walletName,
        100,
        position.token,
        position.tokenQuote,
        {
          dex: position.dex,
          objectId: position.poolId,
          liquidity: position.liquidity,
        } as any
      );
      usdBalance = multipliedBN(
        tokenOut || 0,
        quotePricesRef.current[
          position?.tokenQuote?.address || SUI_TOKEN_ADDRESS_FULL
        ]?.priceUsd
      );
    } catch (error) {
      console.error("Error attachEstimateOut", error);
    }

    const updatedPosition = {
      ...position,
      balanceToUsd: usdBalance,
      balanceToQuote: tokenOut,
      balanceToSui: dividedBN(usdBalance, suiPriceUsdRef.current),
    };

    const positionWithNewPnl = calcPositionPnL(updatedPosition);
    return positionWithNewPnl;
  };

  const handleWhenPositionUpdated = async (event: TBroadcastEvent) => {
    const data: TPositionUpdatedSocket = event.detail;
    const makerAddress = data.walletAddress;
    const currentTime = Date.now();

    const updatedPositions = [...(positionsRef.current || [])];

    const existingPositionIndex = updatedPositions.findIndex(
      (p) =>
        normalizeSuiAddress(p.walletName) ===
          normalizeSuiAddress(makerAddress) && p.pair === data.pairId
    );

    if (existingPositionIndex !== -1) {
      // Update existing position with data from event and extend balance update time
      updatedPositions[existingPositionIndex] = {
        ...(positionsRef.current || [])[existingPositionIndex],
        baseAmountBought: data.baseAmountBought,
        quoteAmountBought: data.quoteAmountBought,
        baseAmountSold: data.baseAmountSold,
        quoteAmountSold: data.quoteAmountSold,
        volumeUsdBought: data.volumeUsdBought,
        volumeUsdSold: data.volumeUsdSold,
        balanceUpdateAt: currentTime + 5000, // Set balance update time after 5s
      };
      positionsRef.current = updatedPositions;
      dispatch(setPosition(updatedPositions[existingPositionIndex]));
    } else {
      // Create new position with data from event
      const tokenData = await rf
        .getRequest("TokenRequest")
        .getTopPair(NETWORKS.SUI, data?.token?.address);
      const newPosition: TPositionWithUpdateTime = {
        balance: data.balance, // Use balance from event
        baseAmountBought: data.baseAmountBought,
        quoteAmountBought: data.quoteAmountBought,
        baseAmountSold: data.baseAmountSold,
        quoteAmountSold: data.quoteAmountSold,
        volumeUsdBought: data.volumeUsdBought,
        volumeUsdSold: data.volumeUsdSold,
        walletName: makerAddress,
        dex: tokenData.dex.dex as EDex,
        poolId: tokenData.poolId as string,
        liquidity: tokenData.liquidity as string,
        token: tokenData.tokenBase,
        tokenBase: tokenData.tokenBase,
        tokenQuote: tokenData.tokenQuote,
        slug: tokenData.slug,
        pair: tokenData.pairId,
        balanceUpdateAt: currentTime + 5000, // Set balance update time after 5s
      } as TPositionWithUpdateTime;

      positionsRef.current = [...(positionsRef.current || []), newPosition];
      dispatch(setPosition(newPosition));
      subscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.PAIR_DETAIL(newPosition.pair)
      );
    }
  };

  const calcPositionPnL = (
    position: TPositionWithUpdateTime
  ): TPositionWithUpdateTime => {
    const totalPnlValue = new BigNumber(position?.balanceToUsd || 0)
      .plus(position.volumeUsdSold || 0)
      .minus(position.volumeUsdBought || 0);

    return {
      ...position,
      simulateAt: new Date().valueOf(),
      pnlToUsd: totalPnlValue.toString(),
      pnlToPercent: isZero(position.volumeUsdBought || 0)
        ? "0"
        : dividedBN(totalPnlValue, position.volumeUsdBought),
    };
  };

  useEffect(() => {
    // setInterval to check debouncedPnLPositionNeedUpdate and attachEstimateOut after each 2 seconds
    const interval = setInterval(() => {
      Array.from(debouncedPnLPositionNeedUpdate.current).forEach((pairId) => {
        const positions = positionsRef.current?.filter(
          (p) => p.pair === pairId
        );
        if (!positions?.length) return;

        Promise.all(
          positions.map((position) => attachEstimateOut(position))
        ).then((updatedPositions) => {
          positionsRef.current = [
            ...(positionsRef.current?.filter((p) => p.pair !== pairId) || []),
            ...updatedPositions,
          ];
          updatedPositions.forEach((updated) => {
            dispatch(setPosition(updated));
          });
          debouncedPnLPositionNeedUpdate.current.delete(pairId);
        });
      });
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const handleWhenNewTransaction = async (event: TBroadcastEvent) => {
    const data: TPairTransaction = event.detail;
    if (isZero(data.priceUsd) || isZero(data.price)) return;
    const updatedPositions = [...(positionsRef.current || [])];
    const pairPositions = updatedPositions?.filter(
      (p) => p.pair === data.pairId
    );
    if (!positionsRef.current || !pairPositions.length) return;

    // Update price for all positions in pair
    pairPositions.forEach((position) => {
      const positionIndex = updatedPositions.findIndex(
        (p) => p.pair === position.pair && p.walletName === position.walletName
      );
      if (positionIndex === -1) return;

      const prevPriceUsd = updatedPositions[positionIndex].priceUsd || "0";
      updatedPositions[positionIndex] = {
        ...updatedPositions[positionIndex],
        price: data.price,
        priceUsd: data.priceUsd,
      };

      if (
        !isZero(prevPriceUsd) &&
        new BigNumber(dividedBN(data.priceUsd, prevPriceUsd)).comparedTo(
          1.01
        ) >= 0
      ) {
        console.log(
          `Trigger calc pnl for ${position.token.symbol} when price change >= 1%`
        );
        debouncedPnLPositionNeedUpdate.current.add(position.pair);
      }
    });

    // Update positions state
    positionsRef.current = updatedPositions;
    pairPositions.forEach((position) => {
      dispatch(setPosition(position));
    });
  };

  useEffect(() => {
    if (!accessToken || !connectedSocket) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.POSITION_UPDATED,
      handleWhenPositionUpdated
    );

    getUserPositions();
    AppBroadcast.on(
      BROADCAST_EVENTS.TRANSACTION_CREATED,
      handleWhenNewTransaction
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.POSITION_UPDATED,
        handleWhenPositionUpdated
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.TRANSACTION_CREATED,
        handleWhenNewTransaction
      );
    };
  }, [accessToken, connectedSocket]);
  return null;
});

PositionDataHandler.displayName = "PositionDataHandler";

const OrderDataHandler = memo(() => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const dispatch = useDispatch<AppDispatch>();

  const getUserOpenOrders = async () => {
    const limit = 100;
    let page = 1;
    let allDocs: any[] = [];
    let hasNextPage = true;

    while (hasNextPage) {
      const res = await rf
        .getRequest("NewOrderRequest")
        .getOpenOrders(NETWORKS.SUI, {
          page,
          limit,
        });

      allDocs = [...allDocs, ...res.docs];

      if (res.docs?.length === 0) {
        hasNextPage = false;
      } else {
        page++;
      }
      await sleep(200);
    }
    // sort by createdAt
    allDocs = allDocs.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    dispatch(setOpenOrders(allDocs));
  };

  const handleWhenOrderUpdated = (event: TBroadcastEvent) => {
    const data: TWsOrder = event.detail;
    if (data.status === "created") {
      return;
    }
    if (data.status !== "success") {
      return toastError("Error", data?.reason || "");
    }
    if (["sellLimit", "buyLimit", "buyDip"].includes(data.orderType)) {
      AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
    }

    const getMsg = () => {
      if (["quickBuy", "buyLimit", "buyDip"].includes(data.orderType)) {
        return `You bought ${data.amountOut} ${data?.token?.symbol}`;
      }

      if (["quickSell", "sellLimit"].includes(data.orderType)) {
        return `You sold ${new BigNumber(data.amountIn)?.abs()?.toString()} ${
          data?.token?.symbol
        }`;
      }

      return "";
    };

    toastSuccess("Success", getMsg(), {
      link: getLinkTxExplorer(data?.network, data.hash),
      text: "View Explorer",
    });
  };

  useEffect(() => {
    if (!accessToken || !connectedSocket) return;
    getUserOpenOrders();
    AppBroadcast.on(BROADCAST_EVENTS.FETCH_ORDERS, getUserOpenOrders);
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, getUserOpenOrders);
    AppBroadcast.on(BROADCAST_EVENTS.ORDER_UPDATED, handleWhenOrderUpdated);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.FETCH_ORDERS, getUserOpenOrders);
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, getUserOpenOrders);
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDER_UPDATED,
        handleWhenOrderUpdated
      );
    };
  }, [accessToken, connectedSocket]);

  return null;
});

OrderDataHandler.displayName = "OrderDataHandler";

const WalletTrackerHandler = memo(() => {
  const [group, setGroup] = useState<TGroup>();
  const [groups, setGroups] = useState<TGroup[]>([]);
  const [wallets, setWallets] = useState<TWalletTracker[]>([]);
  const audioRef = useRef<HTMLAudioElement>(null);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const walletTracker = useSelector(
    (state: RootState) => state.user.walletTracker
  );
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const dispatch = useDispatch<AppDispatch>();

  const getGroups = async () => {
    if (!accessToken) return;
    try {
      const res = await rf.getRequest("WalletTrackerRequest").getGroups();
      if (res.length) {
        setGroups(res);
        setGroup(res[0]);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getGroups().then();
  }, [accessToken]);

  const getWallets = async () => {
    if (!groups || !groups.length || !accessToken) return;
    try {
      let allWallets: any[] = [];
      for (const g of groups) {
        const res = await rf
          .getRequest("WalletTrackerRequest")
          .getWallets(g.id);
        if (Array.isArray(res)) {
          allWallets = allWallets.concat(res);
        }
      }

      dispatch(
        setWalletTracker({
          walletTracker: {
            ...walletTracker,
            wallets: allWallets,
          },
        })
      );
      setWallets(allWallets);
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    if (!connectedSocket || !wallets.length || !accessToken) return;
    wallets?.forEach((item: TWalletTracker) => {
      subscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.MAKER(item.walletAddress)
      );
    });
    return () => {
      wallets?.forEach((item: TWalletTracker) => {
        unsubscribeSocketRoom(
          NETWORKS.SUI,
          SOCKETS_ROOMS.MAKER(item.walletAddress)
        );
      });
    };
  }, [connectedSocket, wallets, accessToken]);

  const handleWhenMakerTrade = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    const wallet = walletTracker?.wallets?.find(
      (w) => w.walletAddress === data?.walletAddress
    );
    toast(
      <ToastNotification
        transaction={data}
        walletName={wallet?.walletName}
        walletEmoji={wallet?.emoji}
      />,
      { autoClose: walletTracker?.settings?.duration * 1000 }
    );
    if (
      walletTracker?.settings?.isEnabledSound &&
      audioRef.current &&
      wallet?.soundNotification
    ) {
      audioRef.current.volume = walletTracker?.settings?.soundVolume / 100;
      audioRef.current.currentTime = 0;
      audioRef.current.play();
    }
  };

  useEffect(() => {
    if (!walletTracker?.settings?.isEnabledToast) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
      handleWhenMakerTrade
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.MAKER_TRADE_SUCCEEDED,
        handleWhenMakerTrade
      );
    };
  }, [walletTracker]);

  useEffect(() => {
    getWallets().then();
  }, [group, accessToken]);

  useEffect(() => {
    if (!accessToken || !connectedSocket) return;
  }, [accessToken, connectedSocket]);

  return (
    <audio
      className="hidden"
      ref={audioRef}
      src="/sounds/mixkit-correct-answer-tone-2870.wav"
      preload="auto"
    />
  );
});

WalletTrackerHandler.displayName = "WalletTrackerHandler";

export const WalletProvider = ({ children }: { children: ReactNode }) => {
  return (
    <WalletContext.Provider value={{}}>
      <PersistPositionDataHandler />
      <PositionDataHandler />
      <WalletDataHandler />
      <OrderDataHandler />
      <WalletTrackerHandler />
      {children}
    </WalletContext.Provider>
  );
};
