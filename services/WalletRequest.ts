import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class WalletRequest extends BaseRequest {
  getUrlPrefix() {
    return config.walletApiUrl;
  }

  async getWallets(network: string) {
    const url = `/${network}/user-wallets`;
    return this.get(url);
  }

  async getGenerateWallets(network: string, params: { numberWallets: number }) {
    const url = `/${network}/user-wallets/generate`;
    return this.post(url, params);
  }

  async editNameWallet(
    network: string,
    walletAddress: string,
    params: { aliasName: string }
  ) {
    const url = `/${network}/user-wallets/${walletAddress}`;
    return this.put(url, params);
  }

  async getImportWallet(network: string, params: { privateKey: string }) {
    const url = `/${network}/user-wallets/import`;
    return this.post(url, params);
  }

  async inactiveAllWallets(network: string) {
    const url = `/${network}/user-wallets/deactivate-all`;
    return this.put(url);
  }

  async inactiveWallet(network: string, walletAddresses: string[]) {
    const url = `/${network}/user-wallets/deactivate`;
    return this.put(url, { walletAddresses });
  }
}
