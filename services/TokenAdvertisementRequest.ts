import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class TokenAdvertisementRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  async getTokenAdvertisement(network: string) {
    const url = `/${network}/tokens/advertisements`;
    return this.get(url);
  }

  async checkActiveTokenAdvertisement(network: string, tokenAddress: string) {
    const url = `/${network}/tokens/advertisements/check-active?tokenAddress=${tokenAddress}`;
    return this.get(url);
  }
}
