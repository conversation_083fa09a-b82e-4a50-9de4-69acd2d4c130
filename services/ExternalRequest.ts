import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class ExternalRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiExternalUrl;
  }
  async postTokenProfile(data: FormData) {
    const url = `/api/v1/token-profiles`;
    return this.post(url, data);
  }

  async getPoolVerifyHistory({
    walletAddress,
    tokenAddress,
  }: {
    walletAddress?: string;
    tokenAddress?: string;
  }) {
    const url = `/api/v1/token-profiles`;
    return this.get(url, {
      walletAddress,
      tokenAddress,
    });
  }
}
