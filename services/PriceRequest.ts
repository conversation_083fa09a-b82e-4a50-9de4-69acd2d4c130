import config from "@/config";
import BaseRequest from "./BaseRequest";

const APT_PRICE_QUERY = "0x1%3A%3Aaptos_coin%3A%3AAptosCoin";
const SUI_PRICE_QUERY =
  "0x0000000000000000000000000000000000000000000000000000000000000002%3A%3Asui%3A%3ASUI";

export default class PriceRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  async getSuiPrice() {
    const url = `/sui/tokens/${SUI_PRICE_QUERY}`;
    return this.get(url);
  }

  async getAptPrice() {
    const url = `/aptos/tokens/${APT_PRICE_QUERY}`;
    return this.get(url);
  }

  async getCoinPrice(network: string, coinAddress: string) {
    const url = `/${network}/tokens/${coinAddress}`;
    return this.get(url);
  }

  async getLockedAmount(
    network: string,
    params: { walletsSelected: string[] }
  ) {
    const url = `/${network}/tokens/locked`;

    const queryParams = new URLSearchParams();
    if (params && params.walletsSelected) {
      queryParams.append("wallets", params.walletsSelected.join(","));
    }

    return this.get(`${url}?${queryParams.toString()}`);
  }
}
