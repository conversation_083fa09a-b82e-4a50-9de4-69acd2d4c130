import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class TokenRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }
  async getTopPair(network: string, tokenAddress: string) {
    const url = `/${network}/tokens/${tokenAddress}/top-pair`;
    return this.get(url);
  }
  async getBalanceOf(network: string, tokenAddress: string) {
    const url = `/${network}/tokens/get-balance-of`;
    return this.get(url, { tokenAddress });
  }
  async getTokenInfo(network: string, tokenAddress: string) {
    const url = `/${network}/tokens/${tokenAddress}/infos`;
    return this.get(url);
  }

  async checkHoneypot(network: string, tokenAddress: string) {
    const url = `/${network}/tokens/${tokenAddress}/honey-pot`;
    return this.get(url);
  }
}
