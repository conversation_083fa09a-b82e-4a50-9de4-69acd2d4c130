import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class WithdrawRequest extends BaseRequest {
  getUrlPrefix() {
    return config.newOrderUrl;
  }

  async transfer(
    network: string,
    params: {
      amountIn: string;
      fromWallet: string;
      toWallet: string;
      metadata?: any;
      paymentCategory?: string;
      withdrawalType?: string;
    }
  ) {
    const url = `/${network}/withdraw/transfer`;
    return this.post(url, params);
  }

  async transferToken(
    network: string,
    tokenAddress: string,
    params: {
      amountIn: string;
      fromWallet: string;
      toWallet: string;
      metadata?: any;
      paymentCategory?: string;
      withdrawalType?: string;
    }
  ) {
    const url = `/${network}/withdraw/transfer/${tokenAddress}`;
    return this.post(url, params);
  }

  async consolidate(
    network: string,
    params: {
      fromWallets: string[];
      consolidatePercent: string | number;
      toWallet: string;
    }
  ) {
    const url = `/${network}/withdraw/consolidate`;
    return this.post(url, params);
  }

  async distribute(
    network: string,
    params: {
      fromWallet: string;
      amountIn: string;
      toWallets: string[];
    }
  ) {
    const url = `/${network}/withdraw/distribute`;
    return this.post(url, params);
  }
}
