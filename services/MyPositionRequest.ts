import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class MyPositionRequest extends BaseRequest {
  getUrlPrefix() {
    return config.appInsightUrl;
  }

  async getMyPositions(network: string, params?: any) {
    const url = `/${network}/api/v1/my/positions`;
    return this.get(url, params);
  }

  async getMyPositionByTokenAddress(network: string, tokenAddress: string) {
    const url = `/${network}/api/v1/my/positions/${tokenAddress}`;
    return this.get(url);
  }

  async hiddenPosition(network: string, tokenAddress: string) {
    const url = `/${network}/api/v1/my/positions/${tokenAddress}/hide`;
    return this.post(url);
  }

  async showPosition(network: string, tokenAddress: string) {
    const url = `/${network}/api/v1/my/positions/${tokenAddress}/show`;
    return this.post(url);
  }
}
