import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class TransactionRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  async getTransactions(network: string, params: any) {
    const url = `/${network}/transactions`;
    return this.get(url, params);
  }

  async getTransactionsByWalletType(network: string, params: any) {
    const url = `/${network}/transactions/filter?`;
    return this.getWithoutEncode(url, params);
  }

  async getDevTrades(network: string, pairId: string, params: any) {
    const url = `/${network}/pairs/${pairId}/transactions/dev`;
    return this.get(url, params);
  }

  async getMyTransactions(network: string, params: any) {
    const url = `/${network}/transactions/user-txs`;
    return this.get(url, params);
  }
}
