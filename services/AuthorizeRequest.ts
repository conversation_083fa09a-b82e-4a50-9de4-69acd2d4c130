import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class AuthorizeRequest extends BaseRequest {
  getUrlPrefix() {
    return config.authorizeApiUrl;
  }
  authorizeApp(params: {
    action: string;
    accessDuration: number;
    clientId: string;
    scopes: string;
  }) {
    const { action, ...bodyParams } = params;
    const url = `/authorize/consent?action=${action}`;
    return this.post(url, bodyParams);
  }
  createUserClient(body: {
    logoUri: string;
    name: string;
    redirectUris: string[];
  }) {
    const url = `/clients`;
    return this.post(url, body);
  }
  checkRedirectUri(params: { clientId: string; redirectUri: string }) {
    const url = `/authorize/check-redirect`;
    return this.get(url, params);
  }
  getPublicInfoClient(params: { clientId: string }) {
    const url = `/clients/public/${params.clientId}`;
    return this.get(url);
  }
  getPermissions() {
    const url = `/clients/permissions`;
    return this.get(url);
  }
}
