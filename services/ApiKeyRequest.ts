import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class ApiKeyRequest extends BaseRequest {
  getUrlPrefix() {
    return config.userApiUrl;
  }

  async getListApiKey() {
    const url = `/api-keys`;
    return this.get(url);
  }

  async createApiKey() {
    const url = `/api-keys`;
    return this.post(url);
  }

  async deleteApiKey(params: { apiKey: string }) {
    const url = `/api-keys?apiKey=${params.apiKey}`;
    return this.delete(url);
  }

  async getApiKeyUsage(params: { apiKey: string }) {
    const url = `/api-keys/usage`;
    return this.get(url, params);
  }

  async getUserUsage() {
    const url = `/api-keys/user/usage`;
    return this.get(url);
  }

  async getPlanSettings(params: { level: string }) {
    const url = `/api-key-settings`;
    return this.get(url, params);
  }

  async getUserPlan() {
    const url = `/user-plan`;
    return this.get(url);
  }
}
