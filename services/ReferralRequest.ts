import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class ReferralRequest extends BaseRequest {
  getUrlPrefix() {
    return config.userApiUrl;
  }

  async getReferralMetadata() {
    const url = `/accounts/referral`;
    return this.get(url);
  }

  async registerReferralCode(myReferralCode: string) {
    const url = `/accounts/referral`;
    return this.put(url, { myReferralCode });
  }
}
