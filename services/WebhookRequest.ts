import config from "@/config";
import { WEBHOOK_EVENTS } from "@/enums/webhook.enum";
import BaseRequest from "./BaseRequest";

export default class WebhookRequest extends BaseRequest {
  getUrlPrefix() {
    return config.webhookApiUrl;
  }

  async getListWebhook() {
    const url = `/webhooks`;
    return this.get(url);
  }

  async createWebhook(body: {
    registeredEvents: WEBHOOK_EVENTS[];
    webhookUrl: string;
    name: string;
  }) {
    const url = `/webhooks`;
    return this.post(url, body);
  }

  async editWebhook(
    params: { webhookId: string },
    body: {
      registeredEvents: WEBHOOK_EVENTS[];
      webhookUrl: string;
      name: string;
    }
  ) {
    const url = `/webhooks/${params.webhookId}`;
    return this.put(url, body);
  }

  async enableWebhook(params: { webhookId: string }) {
    const url = `/webhooks/enable/${params.webhookId}`;
    return this.put(url);
  }

  async disableWebhook(params: { webhookId: string }) {
    const url = `/webhooks/disable/${params.webhookId}`;
    return this.put(url);
  }

  async deleteWebhook(params: { webhookId: string }) {
    const url = `/webhooks?webhookId=${params.webhookId}`;
    return this.delete(url);
  }

  async getWebhookHistories(params: any) {
    const url = `/webhook-histories`;
    return this.get(url, params);
  }
}
