import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { AppButton } from "@/components";
import { formatShortAddress } from "@/utils/format";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { setIsShowModalEnableTradingPrivy } from "@/store/metadata.store";
import { usePrivyTradingEnablement } from "@/hooks/usePrivyTradingEnablement";

export const ModalEnableTradingPrivy = () => {
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalEnableTradingPrivy
  );
  const dispatch = useDispatch<AppDispatch>();
  const wallets = useSelector((state: RootState) => state.user.wallets);

  const onClose = () => {
    dispatch(setIsShowModalEnableTradingPrivy({ isShow: false }));
  };

  const title = "Enable Trading";
  const description =
    "Are you sure you want to authorize RaidenX to create and execute transactions on your wallet?";

  const onEnabled = async () => {
    onClose();
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={title}
      onClose={onClose}
      description={description}
      headerClassName="mt-[12px]"
    >
      <div className="mb-[20px] mt-[16px]">
        <div className="text-white-700 body-sm-regular-12 text-center">
          <span className="text-white-500">Wallet: </span>
          <span className="text-white-900 font-medium">
            {formatShortAddress(wallets[0]?.address, 6, 4)}
          </span>
        </div>
      </div>

      <div className="flex justify-center gap-[8px]">
        <AppButton size="large" onClick={onClose} variant="outline">
          Cancel
        </AppButton>
        <AppButton size="large" onClick={onEnabled} variant={"buy"}>
          Enable
        </AppButton>
      </div>
    </BaseModal>
  );
};
