"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useEffect, useMemo, useState } from "react";
import {
  AppButton,
  AppSelectWallet,
  AppMultiSelectWallet,
  AppNumber,
} from "@/components";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { NumericFormat } from "react-number-format";
import BigNumber from "bignumber.js";
import {
  dividedBN,
  getSymbolTokenNative,
  isValidSuiAddress,
} from "@/utils/helper";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { NETWORKS } from "@/utils/contants";

const EST_FEE = 0.01;

export const ModalDistribute = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [fromWallet, setFromWallet] = useState<string>("");
  const [toWallets, setToWallets] = useState<string[]>([]);
  const [amount, setAmount] = useState<any>("");
  const [percent, setPercent] = useState<any>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onDistribute = async () => {
    try {
      setIsSubmitting(true);
      await rf.getRequest("WithdrawRequest").distribute(NETWORKS.SUI, {
        fromWallet,
        toWallets,
        amountIn: amount,
      });
      toastSuccess("Success", "The transfer has been completed successfully!");
      setIsSubmitting(false);
      onClose();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      setIsSubmitting(false);
      console.error(e);
    }
  };

  const dataFromWallet = useMemo(
    () => wallets.find((item) => item.address === fromWallet),
    [fromWallet]
  );

  const listToWallet = wallets.filter((item) => item.address !== fromWallet);

  const listFromWallet = wallets.filter(
    (item) => !toWallets.includes(item.address)
  );

  const balanceAvailable = useMemo(() => {
    if (new BigNumber(dataFromWallet?.balance || 0).comparedTo(EST_FEE) < 0)
      return 0;
    return new BigNumber(dataFromWallet?.balance || 0).minus(EST_FEE);
  }, [dataFromWallet]);

  useEffect(() => {
    if (!balanceAvailable) {
      setPercent("0");
      return;
    }

    const newPercent = new BigNumber(dividedBN(amount, balanceAvailable))
      .multipliedBy(100)
      .decimalPlaces(4, BigNumber.ROUND_DOWN)
      .toString();
    setPercent(newPercent);
  }, [balanceAvailable]);

  const handleAmountChange = (amount: string) => {
    if (!amount || !balanceAvailable) {
      setPercent("0");
      setAmount(amount);
      return;
    }
    const newPercent = new BigNumber(dividedBN(amount, balanceAvailable))
      .multipliedBy(100)
      .decimalPlaces(4, BigNumber.ROUND_DOWN)
      .toString();
    setPercent(newPercent);
    setAmount(amount);
  };

  const handlePercentChange = (percent: string) => {
    if (!percent || !balanceAvailable) {
      setAmount("0");
      setPercent(percent);
      return;
    }
    const newAmount = new BigNumber(dividedBN(percent, 100))
      .multipliedBy(balanceAvailable)
      .toString();

    setAmount(newAmount);
    setPercent(percent);
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Distribute"}
      onClose={onClose}
      description={
        "Distribute funds from a wallet to many other wallets. All recipient wallets will receive an equal number of tokens entered."
      }
      descClassName="max-w-[388px]"
    >
      <div className="body-xs-regular-10 text-white-500 mb-[16px] text-center">
        Ex. If you enter 10 {getSymbolTokenNative(NETWORKS.SUI)}, each recipient
        wallet will receive 10 {getSymbolTokenNative(NETWORKS.SUI)}
      </div>

      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            From Wallet
          </div>
          {!!fromWallet && (
            <div className="body-xs-regular-10 text-white-500 flex gap-1">
              Balance: <AppNumber value={dataFromWallet?.balance} />{" "}
              {getSymbolTokenNative(NETWORKS.SUI)}
            </div>
          )}
        </div>

        <AppSelectWallet
          walletAddressSelected={fromWallet}
          wallets={listFromWallet}
          onSelect={setFromWallet}
          isOnlySelect
        />
      </div>

      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            To Wallet
          </div>
          {!!toWallets.length && (
            <div className="body-xs-regular-10 text-white-500">
              {toWallets.length} selected
            </div>
          )}
        </div>
        <AppMultiSelectWallet
          walletAddressesSelected={toWallets}
          wallets={listToWallet}
          onSelect={setToWallets}
        />
      </div>

      <div className="grid grid-cols-5 gap-2">
        <div className="col-span-3 w-full">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            Amount
          </div>
          <div>
            <NumericFormat
              placeholder={`Enter amount ${getSymbolTokenNative(NETWORKS.SUI)}`}
              value={amount}
              onChange={(e) =>
                handleAmountChange(e.target.value.replace(/,/g, ""))
              }
              thousandSeparator=","
              valueIsNumericString
              decimalScale={6}
              className="body-sm-regular-12 border-white-100 placeholder:text-white-300 bg-white-50 w-full rounded-[6px] border p-[8px] outline-none"
            />
            {new BigNumber(amount).comparedTo(0) > 0 &&
              new BigNumber(amount).comparedTo(balanceAvailable) > 0 && (
                <div className="body-xs-regular-10 text-red-600">
                  Insufficient balance
                </div>
              )}
          </div>
        </div>
        <div className="col-span-2 flex flex-col items-end">
          <div className="body-xs-regular-10 text-white-700 mb-[8px]">
            Est Fee: {EST_FEE} {getSymbolTokenNative(NETWORKS.SUI)}
          </div>
          <div className="body-sm-regular-12 border-white-100 bg-white-50 flex gap-2 rounded-[6px] border p-[8px]">
            <NumericFormat
              value={percent}
              onChange={(e) =>
                handlePercentChange(e.target.value.replace(/,/g, ""))
              }
              thousandSeparator=","
              decimalScale={4}
              className="body-sm-regular-12 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
            />
            <div className="text-white-500">%</div>
          </div>
        </div>
      </div>

      <div className="mt-[20px] flex justify-center">
        <AppButton
          disabled={
            toWallets.some((item) => !isValidSuiAddress(item)) ||
            isSubmitting ||
            !fromWallet ||
            !toWallets.length ||
            !amount ||
            new BigNumber(amount).comparedTo(dataFromWallet?.balance || 0) > 0
          }
          size="large"
          onClick={onDistribute}
          className="w-[160px]"
        >
          {isSubmitting ? "Submitting" : "Confirm"}
        </AppButton>
      </div>
    </BaseModal>
  );
};
