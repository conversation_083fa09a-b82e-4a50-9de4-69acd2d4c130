"use client";

import { normalizeStructTag } from "@mysten/sui/utils";
import clsx from "clsx";
import { debounce, groupBy, isEmpty, orderBy } from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import ReactModal from "react-modal";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import Link from "next/link";
import { CloseIcon, FireIcon, SearchIcon } from "@/assets/icons";
import { AppAvatarToken, AppCopy, AppNumber, BaseToken } from "@/components";
import { WarningLiquidity } from "@/components/ListPair";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import Storage, { THistorySearch } from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { DEXS, getDexLogoUrl } from "@/utils/dex";
import {
  formatAgeTime,
  formatNumber,
  formatShortAddress,
} from "@/utils/format";
import { isValidCAAddress, multipliedBN, toStringBN } from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import { NETWORKS } from "@/utils/contants";

const TrendingItem = ({
  network,
  no,
  trendingPair,
  onClose,
  onSelectPair,
}: {
  network: string;
  trendingPair: TPair;
  no: number;
  onClose: () => void;
  onSelectPair?: (slug: string) => void;
}) => {
  return (
    <Link
      key={no}
      onClick={(e) => {
        if (onSelectPair) {
          e.preventDefault();
          return;
        }
        if (!e.defaultPrevented) {
          onClose();
        }
      }}
      href={`/${network}/${trendingPair.slug}`}
    >
      <div
        onClick={(e) => {
          if (!e.defaultPrevented) {
            if (onSelectPair) {
              onSelectPair(trendingPair.slug);
              onClose();
              return;
            }
          }
        }}
        className="hover:bg-white-50 border-white-50 flex cursor-pointer items-center gap-1 border-b py-[12px] md:px-[8px]"
      >
        <div className="flex items-center text-yellow-500">
          <FireIcon />
          <div className="body-sm-medium-12 md:body-md-medium-14 w-[26px]">
            #{no}
          </div>
        </div>
        <div className="flex items-center gap-1">
          <BaseToken pair={trendingPair} />
          <div className="w-[120px] md:w-[200px]">
            <div className="flex items-center gap-2 ">
              <div className="md:body-md-medium-14 body-sm-medium-12">
                {trendingPair?.tokenBase?.symbol || "Unknown"}
              </div>
              <div className="body-sm-regular-12 md:body-md-regular-14 text-white-700 max-w-[50px] truncate">
                {trendingPair?.tokenBase?.symbol
                  ? trendingPair?.tokenBase?.name
                  : "Unknown"}
              </div>

              <WarningLiquidity pair={trendingPair} />
            </div>

            <div className="flex items-center gap-2">
              <div className="body-xs-medium-10 md:body-sm-medium-12 border-white-50 flex items-center gap-1 border-r pr-2">
                {formatShortAddress(trendingPair?.tokenBase?.address, 4, 4)}
                <div onClick={(e) => e.preventDefault()}>
                  <AppCopy message={trendingPair?.tokenBase?.address} />
                </div>
              </div>
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                {formatAgeTime(trendingPair?.timestamp * 1000)}
              </div>
            </div>
            <div className="mt-1 flex gap-2 md:hidden">
              <div className="text-white-800 body-xs-regular-10 flex gap-1">
                <div className="text-white-500">VOL</div>
                <AppNumber value={trendingPair?.volumeUsd} isForUSD />
              </div>
              <div className="text-white-800 body-xs-regular-10 flex gap-1">
                <div className="text-white-500">LIQ</div>
                <AppNumber value={trendingPair?.liquidityUsd} isForUSD />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-1 justify-end md:justify-between">
          <div className="hidden flex-col items-start md:flex">
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                VOL
              </div>
              <AppNumber value={trendingPair?.volumeUsd} isForUSD />
            </div>
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                LIQ
              </div>
              <AppNumber value={trendingPair?.liquidityUsd} isForUSD />
            </div>
          </div>

          <div className="flex flex-col items-end">
            <div className="text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                1H
              </div>
              <div
                className={`body-sm-regular-12 md:body-md-regular-14 ${
                  +trendingPair?.stats?.percent["1h"] > 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {+trendingPair?.stats?.percent["1h"] > 0 ? "+" : ""}
                {formatNumber(trendingPair?.stats?.percent["1h"], 2)}%
              </div>
            </div>
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                24H
              </div>
              <div
                className={`body-sm-regular-12 md:body-md-regular-14 ${
                  +trendingPair?.stats?.percent["24h"] > 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {+trendingPair?.stats?.percent["24h"] > 0 ? "+" : ""}
                {formatNumber(trendingPair?.stats?.percent["24h"], 2)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const ResultItem = ({
  pair,
  network,
  onClose,
  addHistory,
  onSelectPair,
}: {
  pair: TPair;
  network: string;
  onClose: () => void;
  onSelectPair?: (pairSlug: string) => void;
  addHistory: (item: TPair) => void;
}) => {
  const [volume24hUsd, setVolume24hUsd] = useState<string>("0");
  const [priceUsd, setPriceUsd] = useState<string>("0");
  const [liquidityUsd, setLiquidityUsd] = useState<string>("0");

  useEffect(() => {
    setLiquidityUsd(pair?.liquidityUsd);
    setVolume24hUsd(toStringBN(pair?.stats?.volume?.["24h"]));
    setPriceUsd(pair?.tokenBase?.priceUsd);
  }, [pair.pairId]);

  const handleWhenPairStatsChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }
    setVolume24hUsd(data?.stats?.volume?.["24h"]);
    setPriceUsd(data?.priceUsd);
    setLiquidityUsd(data?.liquidityUsd);
  };

  useEffect(() => {
    if (isEmpty(pair.pairId)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.PAIR_STATS_UPDATED,
      handleWhenPairStatsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenPairStatsChange
      );
    };
  }, [pair.pairId]);

  return (
    <Link
      onClick={(e) => {
        if (onSelectPair) {
          e.preventDefault();
          return;
        }
        if (!e.defaultPrevented) {
          onClose();
        }
      }}
      href={`/${network}/${pair.slug}`}
    >
      <div
        onClick={(e) => {
          if (!e.defaultPrevented) {
            if (onSelectPair) {
              onSelectPair(pair.slug);
              onClose();
              return;
            }
            addHistory(pair);
          }
        }}
        className="hover:bg-white-50 border-white-50 flex cursor-pointer items-center gap-1 border-b py-[12px] md:px-[8px]"
      >
        <div className="flex w-[180px] items-center gap-1 md:w-[300px]">
          <BaseToken pair={pair} />
          <div>
            <div className="body-sm-medium-12 md:body-md-medium-14 flex cursor-pointer items-center gap-1">
              {pair?.tokenBase?.symbol || "Unknown"}
              <div className="text-white-700 body-sm-regular-12 md:body-md-regular-14 mr-1 max-w-[100px] truncate md:max-w-[200px]">
                {pair?.tokenBase?.symbol ? pair?.tokenBase?.name : "Unknown"}
              </div>

              <WarningLiquidity pair={pair} />
            </div>

            <div className="flex items-center gap-2">
              <div className="body-xs-regular-10 md:body-sm-regular-12 flex items-center gap-1">
                {formatShortAddress(pair?.tokenBase?.address, 4, 4)}
                <div onClick={(e) => e.preventDefault()}>
                  <AppCopy message={pair?.tokenBase?.address} />
                </div>
              </div>
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500 border-white-50 border-l pl-2">
                {formatAgeTime(pair?.timestamp * 1000)}
              </div>
            </div>
            <div className="mt-1 flex gap-2 md:hidden">
              <div className="body-xs-regular-10 text-white-800 flex gap-1">
                <div className="text-white-500">24h Vol</div>
                <AppNumber value={volume24hUsd} isForUSD />
              </div>
              <div className="text-white-800 body-xs-regular-10 flex gap-1">
                <div className="text-white-500">LIQ</div>
                <AppNumber value={liquidityUsd} isForUSD />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-1 items-center justify-end gap-1 md:justify-between">
          <div className="hidden flex-col items-start md:flex ">
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                24h VOL
              </div>
              <AppNumber value={volume24hUsd} isForUSD />
            </div>
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                LIQ
              </div>
              <AppNumber value={liquidityUsd} isForUSD />
            </div>
          </div>
          <div className="body-sm-regular-12 md:body-md-regular-14 flex flex-col items-end">
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                MC
              </div>
              <AppNumber
                value={multipliedBN(getCirculatingSupply(pair), priceUsd)}
                isForUSD
              />
            </div>
            <div className="body-sm-regular-12 md:body-md-regular-14 text-white-800 flex gap-1">
              <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                24h
              </div>
              <div
                className={`body-sm-regular-12 md:body-md-regular-14 ${
                  +pair?.stats?.percent["24h"] > 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {`${formatNumber(pair?.stats?.percent["24h"], 2)}%`}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const RecentSearchItem = ({
  item,
  onClose,
  onSelectPair,
}: {
  item: THistorySearch;
  onClose: () => void;
  onSelectPair?: (slug: string) => void;
}) => {
  return (
    <Link
      onClick={(e) => {
        if (onSelectPair) {
          e.preventDefault();
          onSelectPair(item.slug);
          return;
        }
      }}
      href={`/${item.network}/${encodeURI(item.slug)}`}
      key={item.slug}
    >
      <div
        onClick={() => {
          onClose();
        }}
        className="body-sm-regular-12 md:body-md-regular-14 hover:bg-white-50 border-white-50 flex w-max cursor-pointer items-center gap-1 rounded-[56px] border p-[4px] pr-[10px]"
      >
        <div className="relative">
          <AppAvatarToken
            className="h-[24px] w-[24px]"
            image={item?.image}
            size={24}
          />

          <div
            className="absolute bottom-[-2px] right-[-2px] rounded-full p-[2px]"
            style={{
              background: "rgba(8, 9, 12, 0.80)",
            }}
          >
            <img
              src={getDexLogoUrl(item.dex as keyof typeof DEXS)}
              className="h-[12px] w-[12px]"
            />
          </div>
        </div>
        {item.name}
      </div>
    </Link>
  );
};

export const ModalSearch = ({
  isOpen,
  onClose,
  onSelectPair,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSelectPair?: (pairSlug: string) => void;
}) => {
  const [search, setSearch] = useState<string>("");
  const [results, setResults] = useState<TPair[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataHistorySearch, setDataHistorySearch] = useState<THistorySearch[]>(
    []
  );
  const [isShowPoolsLowerLiquidity, setIsShowPoolsLowerLiquidity] =
    useState<boolean>(false);

  const trendingPairsMeme = useSelector(
    (state: RootState) => state.metadata.trendingPairsMeme
  );

  const debounceSearch = useCallback(
    debounce((nextValue: string) => onSearch(nextValue), 1000),
    []
  );

  const historySearch = Storage.getHistorySearch() as THistorySearch[];

  const addHistory = (pair: TPair) => {
    if (historySearch.some((item: THistorySearch) => pair.slug === item.slug)) {
      return;
    }
    const newHistory = [
      {
        name: pair?.tokenBase?.symbol || "Unknown",
        slug: pair?.slug,
        dex: pair?.dex.dex,
        network: pair?.network,
        image: pair?.tokenBase?.logoImageUrl || pair?.tokenBase?.iconUrl || "",
      },
    ].concat(historySearch);

    Storage.setHistorySearch(newHistory);
  };

  useEffect(() => {
    async function getDetailsRecentSearch() {
      try {
        const res = await Promise.all(
          historySearch.map(async (item) => {
            if (!!item.image) {
              return item;
            }
            const data = await rf
              .getRequest("PairRequest")
              .getPair(NETWORKS.SUI, item?.slug);
            return {
              ...item,
              image:
                data?.tokenBase?.logoImageUrl || data?.tokenBase?.iconUrl || "",
            };
          })
        );
        setDataHistorySearch(res);
      } catch (err) {
        console.error("Error fetching details:", err);
      }
    }
    getDetailsRecentSearch().then();
  }, []);

  const onSearch = async (valueSearch: string) => {
    try {
      setIsLoading(true);
      setIsShowPoolsLowerLiquidity(false);
      const res = await rf.getRequest("SearchRequest").search({
        search: isValidCAAddress(valueSearch)
          ? normalizeStructTag(valueSearch)
          : valueSearch,
      });
      let newResult = res?.docs;
      if (res?.docs?.length) {
        newResult = res?.docs.sort((a: TPair, b: TPair) => {
          return +b?.liquidityUsd - +a?.liquidityUsd;
        });
      }
      setResults(newResult);
      setIsLoading(false);
    } catch (e) {
      setIsLoading(false);
      console.error(e);
    }
  };

  useEffect(() => {
    if (search?.trim()?.length < 2) {
      setResults([]);
    }
  }, [search]);

  const isTabletMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const customStyles = {
    content: {
      top: isTabletMobile ? "0" : "10%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translateX(-50%)",
      borderRadius: "8px",
      border: 0,
      padding: 0,
      background: "#1c1d23",
      overflow: "inherit",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.70)",
      backdropFilter: "blur(7.5px)",
      zIndex: 999,
    },
  };

  const _renderResults = () => {
    if (!results?.length) {
      return <div className="w-full text-center">No results</div>;
    }

    const otherSmallerPools: any[] = [];
    const groupedData = Object.values(groupBy(results, "tokenBase.address"));

    const biggestPools = groupedData.map((item) => {
      otherSmallerPools.push(...item?.slice(1));
      return item?.[0];
    });

    const sortOtherSmallerPools = orderBy(
      otherSmallerPools,
      [(item) => Number(item?.liquidityUsd)],
      ["desc"]
    );

    return (
      <div>
        <div
          className={clsx(
            "customer-scroll flex flex-col  overflow-auto px-[16px]",
            !!otherSmallerPools.length
              ? "max-h-[41vh] md:max-h-[50vh]"
              : "max-h-[calc(100vh-100px)] md:max-h-[65vh]",
            isShowPoolsLowerLiquidity ? "md:!max-h-[35vh]" : ""
          )}
        >
          {biggestPools.map((item, index) => {
            return (
              <ResultItem
                pair={item}
                key={index}
                onClose={onClose}
                network={NETWORKS.SUI}
                addHistory={addHistory}
                onSelectPair={onSelectPair}
              />
            );
          })}
        </div>

        {!!sortOtherSmallerPools.length && (
          <div>
            {isShowPoolsLowerLiquidity ? (
              <div className="pt-[16px]">
                <div className="flex items-center justify-between px-[16px]">
                  <p className="text-[12px] font-medium">
                    Pools with lower liquidity
                  </p>
                  <button
                    className="text-brand-500 p-[8px] text-[12px] font-medium"
                    onClick={() => setIsShowPoolsLowerLiquidity(false)}
                  >
                    Hide
                  </button>
                </div>
                <div className="customer-scroll flex max-h-[41vh] flex-col overflow-auto px-[16px] md:max-h-[35vh]">
                  {sortOtherSmallerPools.map((item, index) => {
                    return (
                      <ResultItem
                        pair={item}
                        key={index}
                        onClose={onClose}
                        network={NETWORKS.SUI}
                        addHistory={addHistory}
                        onSelectPair={onSelectPair}
                      />
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="flex justify-center pt-[16px]">
                <button
                  className="bg-white-100 rounded-6 w-fit p-[8px] text-[12px] font-medium"
                  onClick={() => setIsShowPoolsLowerLiquidity(true)}
                >
                  Show pools with lower liquidity
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
      contentElement={({ className, ...props }, children) => (
        <div {...props} className={`${className}`}>
          {children}
        </div>
      )}
    >
      <div
        className="h-screen w-screen py-[12px] md:h-auto md:min-h-[65vh] md:max-w-[640px] md:rounded-[8px] md:p-[4px] "
        style={{
          background:
            "linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.10) 100%), #08090C",
          boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
        }}
      >
        <div className="flex w-full items-center gap-2 px-[16px]">
          <div className="bg-black-900 border-neutral-alpha-100 flex w-full items-center gap-1 rounded-[6px] border p-[8px]">
            <SearchIcon className="h-[20px] w-[20px]" />
            <input
              autoFocus
              className="body-sm-regular-12 md:body-md-regular-14 flex-1 truncate bg-transparent leading-none outline-none"
              value={search}
              onChange={(e) => {
                const value = e.target.value;
                setIsLoading(true);
                setSearch(e.target.value);

                if (value.trim().length >= 2) {
                  debounceSearch(e.target.value);
                }
              }}
              placeholder="Search"
            />

            {search && (
              <div
                className="text-white-500 hover:text-white-1000 cursor-pointer"
                onClick={() => setSearch("")}
              >
                <CloseIcon className="w-[12px]" />
              </div>
            )}
          </div>

          <div
            className="body-sm-medium-12 block cursor-pointer md:hidden"
            onClick={onClose}
          >
            Cancel
          </div>
        </div>

        {search?.trim()?.length >= 2 && (
          <div>
            {isLoading ? (
              <div className="mt-4 w-full px-[16px] text-center">
                Loading...
              </div>
            ) : (
              <div className="pb-[16px] pt-[8px]">
                <div className="body-sm-regular-12 md:body-md-regular-14 text-white-300 px-[16px]">
                  SEARCH RESULT
                </div>
                {_renderResults()}
              </div>
            )}
          </div>
        )}

        {(!search || !results.length) && (
          <div className="pb-[16px] pt-[8px]">
            {!!dataHistorySearch.length && (
              <div className="px-[16px]">
                <div className="mb-[6px] flex justify-between">
                  <div className="body-sm-regular-12 md:body-md-regular-14 text-white-300 uppercase">
                    Recent Searches
                  </div>
                  <div
                    className="body-sm-medium-12 md:body-md-medium-14 text-white-500 hover:text-white-1000 cursor-pointer"
                    onClick={() => {
                      Storage.clearHistorySearch();
                      setDataHistorySearch([]);
                    }}
                  >
                    Clear all
                  </div>
                </div>
                <div className="mb-4 flex flex-wrap gap-2 md:mb-6">
                  {dataHistorySearch
                    ?.slice(0, 5)
                    ?.map((item: THistorySearch, index) => {
                      return (
                        <RecentSearchItem
                          item={item}
                          onClose={onClose}
                          key={index}
                          onSelectPair={onSelectPair}
                        />
                      );
                    })}
                </div>
              </div>
            )}

            <div>
              <div className="body-sm-regular-12 md:body-md-regular-14 text-white-300 px-[16px]">
                TOP 20 TRENDING PAIR
              </div>
              <div
                className={` ${
                  isLoading || dataHistorySearch.length > 3
                    ? "max-h-[calc(100vh-260px)]"
                    : dataHistorySearch.length
                    ? "max-h-[calc(100vh-270px)]"
                    : "max-h-[calc(100vh-190px)]"
                } customer-scroll flex flex-col gap-1 overflow-auto px-[16px] md:max-h-[calc(65vh-80px)]`}
              >
                {trendingPairsMeme.map((trendingPair, index) => {
                  return (
                    <TrendingItem
                      onSelectPair={onSelectPair}
                      key={index}
                      trendingPair={trendingPair}
                      network={NETWORKS.SUI}
                      onClose={onClose}
                      no={index + 1}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </ReactModal>
  );
};
