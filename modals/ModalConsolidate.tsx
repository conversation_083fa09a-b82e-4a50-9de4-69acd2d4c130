"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useState } from "react";
import { AppButton, AppMultiSelectWallet, AppSelectWallet } from "@/components";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { isValidSuiAddress } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";

export const ModalConsolidate = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [fromWallets, setFromWallets] = useState<string[]>([]);
  const [toWallet, setToWallet] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const listFromWallet = wallets.filter((item) => item.address !== toWallet);

  const listToWallet = wallets.filter(
    (item) => !fromWallets.includes(item.address)
  );

  const onConsolidate = async () => {
    try {
      setIsSubmitting(true);
      await rf.getRequest("WithdrawRequest").consolidate(NETWORKS.SUI, {
        fromWallets,
        toWallet,
        consolidatePercent: 100,
      });
      toastSuccess(
        "Success",
        "The consolidate has been completed successfully!"
      );
      setIsSubmitting(false);
      // dispatch(getWalletsUser({ network: networkSelected }));
      onClose();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      setIsSubmitting(false);
      console.error(e);
    }
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Consolidate"}
      onClose={onClose}
      description={
        "All your funds in your account will be transferred to the recipient wallet"
      }
    >
      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="action-xs-medium-12 text-white-700 mb-[8px]">
            From Wallet
          </div>

          {!!fromWallets.length && (
            <div className="body-xs-regular-10 text-white-500">
              {fromWallets.length} wallets
            </div>
          )}
        </div>

        <AppMultiSelectWallet
          walletAddressesSelected={fromWallets}
          wallets={listFromWallet}
          onSelect={setFromWallets}
          isOnlySelect
        />
      </div>
      <div>
        <div className="action-xs-medium-12 text-white-700 mb-[8px]">
          To Wallet
        </div>

        <AppSelectWallet
          walletAddressSelected={toWallet}
          wallets={listToWallet}
          onSelect={setToWallet}
        />
      </div>
      <div className="mt-[20px] flex justify-center">
        <AppButton
          disabled={
            !isValidSuiAddress(toWallet) ||
            isSubmitting ||
            !fromWallets.length ||
            !toWallet
          }
          size="large"
          onClick={onConsolidate}
          className="w-[160px]"
        >
          {isSubmitting ? "Submitting" : "Confirm"}
        </AppButton>
      </div>
    </BaseModal>
  );
};
