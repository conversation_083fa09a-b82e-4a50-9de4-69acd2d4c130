"use client";

import { BaseModal } from "@/modals/BaseModal";
import React, { useEffect, useState } from "react";
import { AppButton } from "@/components";
import { TWallet } from "@/types";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { useDispatch } from "react-redux";
import { getWalletsUser } from "@/store/user.store";
import { AppDispatch } from "@/store";
import { NETWORKS } from "@/utils/contants";

export const ModalEditWallet = ({
  isOpen,
  onClose,
  wallet,
}: {
  isOpen: boolean;
  onClose: () => void;
  wallet: TWallet;
}) => {
  const [name, setName] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    setName(wallet.aliasName);
  }, [wallet.aliasName]);

  const onEditName = async () => {
    try {
      await rf
        .getRequest("WalletRequest")
        .editNameWallet(NETWORKS.SUI, wallet.address, {
          aliasName: name,
        });
      dispatch(getWalletsUser({ network: NETWORKS.SUI }));
      toastSuccess("Success", "Wallet name was changed!!");
      onClose();
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      title={"Edit Name"}
      onClose={onClose}
      className="w-[calc(100vw-16px)] max-w-[420px]"
    >
      <input
        className="bg-black-900 border-white-100 body-sm-medium-12 w-full rounded-[6px] border p-[8px] outline-none"
        value={name}
        onKeyDown={(e) => {
          if (e.key === " ") {
            e.preventDefault();
          }
        }}
        onChange={(e) => {
          const text = e.target.value.trim();
          if (text.length <= 20) {
            setName(text);
            return;
          }
          return;
        }}
      />
      <div className="mt-[20px] flex justify-center gap-2">
        <AppButton
          size="large"
          className="min-w-[80px]"
          onClick={onClose}
          variant="outline"
        >
          Cancel
        </AppButton>
        <AppButton size="large" className="min-w-[80px]" onClick={onEditName}>
          Save
        </AppButton>
      </div>
    </BaseModal>
  );
};
