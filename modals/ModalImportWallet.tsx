"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { AppButton } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import { BaseModal } from "@/modals/BaseModal";
import rf from "@/services/RequestFactory";
import { getWalletsUser } from "@/store/user.store";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { AppDispatch } from "@/store";
import { NETWORKS } from "@/utils/contants";
export const ModalImportWallet = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const [privateKey, setPrivateKey] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();

  const onImportWallet = async () => {
    try {
      const standardPrivateKey = getStandardPrivateKey(privateKey);
      if (!standardPrivateKey) {
        throw new Error(
          "Private key format is incorrect. Please make sure it starts with suiprivkey and not a passphrase."
        );
      }
      await rf.getRequest("WalletRequest").getImportWallet(NETWORKS.SUI, {
        privateKey: standardPrivateKey,
      });
      dispatch(getWalletsUser({ network: NETWORKS.SUI }));
      toastSuccess("Success", "Import successfully!");
      onSuccess && onSuccess();
      onClose();
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const getStandardPrivateKey = (privateKey: string) => {
    try {
      if (privateKey.startsWith("suiprivkey")) {
        return Ed25519Keypair.fromSecretKey(privateKey).getSecretKey();
      }
      return "";
    } catch (e) {
      return "";
    }
  };

  return (
    <BaseModal
      className="w-[calc(100vw-16px)] max-w-[420px]"
      isOpen={isOpen}
      title={"Import Wallet"}
      onClose={onClose}
      description=""
      descClassName="max-w-[300px]"
    >
      <div className="flex flex-col gap-1">
        <div>
          <div className="body-sm-medium-12 text-white-700 mb-[8px]">
            Input your private keys
          </div>
          <textarea
            placeholder="Enter your private key"
            className="bg-black-900 placeholder:text-white-300 body-sm-regular-12 border-white-100 h-[275px] w-full rounded-[6px] border p-[8px] outline-none"
            value={privateKey}
            onChange={(e) => setPrivateKey(e.target.value.trim())}
          />
        </div>
      </div>
      <div className="mt-[28px] flex justify-center">
        <AppButton
          disabled={!privateKey}
          className="min-w-[167px]"
          onClick={onImportWallet}
          size="large"
        >
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};
