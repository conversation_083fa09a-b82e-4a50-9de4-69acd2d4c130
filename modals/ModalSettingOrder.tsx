"use client";

import React, { useEffect, useState } from "react";
import ReactModal from "react-modal";
import { CloseIcon, CoinTip, GasIcon, SlippageIcon } from "@/assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { NumericFormat } from "react-number-format";
import { AppButton, AppLogoNetwork, AppToggle } from "@/components";
import BigNumber from "bignumber.js";
import { toastError, toastSuccess } from "@/libs/toast";
import { formatNumber } from "@/utils/format";
import { useMediaQuery } from "react-responsive";
import { calculateMaxGasFee } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import rf from "@/services/RequestFactory";
import { AppDispatch } from "@/store/index";
import {
  getSettingsLimitOrder,
  getSettingsSnipeOrder,
  getSettingsQuickOrder,
  getSettingsCopyTradeOrder,
} from "@/store/user.store";

const TABS = [
  {
    name: "SNIPE",
    value: "snipe",
  },
  {
    name: "BUY/SELL",
    value: "buy-sell",
  },
  {
    name: "LIMIT ORDERS",
    value: "limit",
  },
  {
    name: "COPY TRADE",
    value: "copy-trade",
  },
];

const ModalSettingsOrder = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "8px",
      padding: 0,
      background:
        "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
      overflow: "hidden",
      border: 0,
      boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50)",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.30)",
      zIndex: 999,
    },
  };
  const isTablet = useMediaQuery({ maxWidth: 992 });
  const [tab, setTab] = useState<string>("snipe");

  // buy sell
  const [slippage, setSlippage] = useState<any>(40);
  const [tipAmount, setTipAmount] = useState<any>("0.02");
  const [gasPrice, setGasPrice] = useState<any>(750);
  const [isEnableTip, setIsEnableTip] = useState<boolean>(true);

  const maxGasFeeBuySell = calculateMaxGasFee(gasPrice);

  // limit
  const [limitSlippage, setLimitSlippage] = useState<any>(40);
  const [limitTipAmount, setLimitTipAmount] = useState<any>("0.02");
  const [limitGasPrice, setLimitGasPrice] = useState<any>(750);
  const [isEnableLimitTip, setIsEnableLimitTip] = useState<boolean>(true);
  const maxGasFeeLimit = calculateMaxGasFee(limitGasPrice);

  // snipe
  const [snipeSlippage, setSnipeSlippage] = useState<any>(40);
  const [snipeTipAmount, setSnipeTipAmount] = useState<any>("0.02");
  const [snipeGasPrice, setSnipeGasPrice] = useState<any>(750);
  const [isEnableSnipeTip, setIsEnableSnipeTip] = useState<boolean>(true);
  const maxGasFeeSnipe = calculateMaxGasFee(snipeGasPrice);

  //copy trade
  const [copyTradeSlippage, setCopyTradeSlippage] = useState<any>(40);
  const [copyTradeTipAmount, setCopyTradeTipAmount] = useState<any>("0.02");
  const [copyTradeGasPrice, setCopyTradeGasPrice] = useState<any>(750);

  const [isEnableCopyTradeTip, setIsEnableCopyTradeTip] =
    useState<boolean>(true);
  const maxGasFeeCopyTrade = calculateMaxGasFee(copyTradeGasPrice);

  const network = useSelector((state: RootState) => state.user.network);
  const dispatch = useDispatch<AppDispatch>();

  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );

  const settingsLimitOrder = useSelector(
    (state: RootState) => state.user.settingsLimitOrder
  );

  const settingsSnipeOrder = useSelector(
    (state: RootState) => state.user.settingsSnipeOrder
  );

  const settingsCopyTradeOrder = useSelector(
    (state: RootState) => state.user.settingsCopyTradeOrder
  );

  useEffect(() => {
    setTipAmount(settingsQuickOrder?.tipAmount);
    setSlippage(settingsQuickOrder?.slippage);
    setGasPrice(settingsQuickOrder?.gasPrice);

    // limit
    setLimitTipAmount(settingsLimitOrder?.tipAmount);
    setLimitSlippage(settingsLimitOrder?.slippage);
    setLimitGasPrice(settingsLimitOrder?.gasPrice);

    // snipe
    setSnipeTipAmount(settingsSnipeOrder?.tipAmount);
    setSnipeSlippage(settingsSnipeOrder?.slippage);
    setSnipeGasPrice(settingsSnipeOrder?.gasPrice);

    //copytrade
    setCopyTradeTipAmount(settingsCopyTradeOrder?.tipAmount);
    setCopyTradeSlippage(settingsCopyTradeOrder?.slippage);
    setCopyTradeGasPrice(settingsCopyTradeOrder?.gasPrice);
  }, [
    settingsCopyTradeOrder,
    settingsSnipeOrder,
    settingsLimitOrder,
    settingsQuickOrder,
  ]);

  useEffect(() => {
    if (new BigNumber(settingsQuickOrder?.tipAmount).gt(0)) {
      setIsEnableTip(true);
      return;
    }
    setIsEnableTip(false);
  }, [settingsQuickOrder]);

  useEffect(() => {
    if (new BigNumber(settingsLimitOrder?.tipAmount).gt(0)) {
      setIsEnableLimitTip(true);
      return;
    }
    setIsEnableLimitTip(false);
  }, [settingsLimitOrder?.tipAmount]);

  useEffect(() => {
    if (new BigNumber(settingsSnipeOrder?.tipAmount).gt(0)) {
      setIsEnableSnipeTip(true);
      return;
    }
    setIsEnableSnipeTip(false);
  }, [settingsSnipeOrder]);

  useEffect(() => {
    if (new BigNumber(settingsCopyTradeOrder?.tipAmount).gt(0)) {
      setIsEnableCopyTradeTip(true);
      return;
    }
    setIsEnableCopyTradeTip(false);
  }, [settingsCopyTradeOrder]);

  const onApply = async () => {
    if (
      new BigNumber(gasPrice).lt(750) ||
      new BigNumber(limitGasPrice).lt(750) ||
      new BigNumber(snipeGasPrice).lt(750) ||
      new BigNumber(copyTradeGasPrice).lt(750)
    ) {
      toastError("Error", "Minimum gas price is 750 MIST");
      return;
    }

    if (isEnableTip) {
      if (new BigNumber(tipAmount).lt(0.02)) {
        toastError("Error", "Minimum tip amount is 0.02 SUI");
        return;
      }
    }

    if (isEnableSnipeTip) {
      if (new BigNumber(snipeTipAmount).lt(0.02)) {
        toastError("Error", "Minimum tip amount is 0.02 SUI");
        return;
      }
    }

    if (isEnableLimitTip) {
      if (new BigNumber(limitTipAmount).lt(0.02)) {
        toastError("Error", "Minimum tip amount is 0.02 SUI");
        return;
      }
    }

    if (isEnableCopyTradeTip) {
      if (new BigNumber(copyTradeTipAmount).lt(0.02)) {
        toastError("Error", "Minimum tip amount is 0.02 SUI");
        return;
      }
    }

    try {
      // limit order
      await rf
        .getRequest("PresetSettingRequest")
        .updateLimitOrderSettings(network, {
          ...settingsLimitOrder,
          tipAmount: isEnableLimitTip ? +limitTipAmount : 0,
          slippage: +limitSlippage,
          gasPrice: +limitGasPrice,
        });

      // quick order
      await rf
        .getRequest("PresetSettingRequest")
        .updateQuickOrderSettings(network, {
          ...settingsQuickOrder,
          tipAmount: isEnableTip ? +tipAmount : 0,
          slippage: +slippage,
          gasPrice: +gasPrice,
        });

      // snipe
      await rf
        .getRequest("PresetSettingRequest")
        .updateSnipeOrderSettings(network, {
          ...settingsSnipeOrder,
          tipAmount: isEnableSnipeTip ? +snipeTipAmount : 0,
          slippage: +snipeSlippage,
          gasPrice: +snipeGasPrice,
        });

      // copy trade
      await rf
        .getRequest("PresetSettingRequest")
        .updateCopyTradeOrderSettings(network, {
          ...settingsCopyTradeOrder,
          tipAmount: isEnableCopyTradeTip ? +copyTradeTipAmount : 0,
          slippage: +copyTradeSlippage,
          gasPrice: +copyTradeGasPrice,
        });

      dispatch(getSettingsLimitOrder({ network }));
      dispatch(getSettingsSnipeOrder({ network }));
      dispatch(getSettingsQuickOrder({ network }));
      dispatch(getSettingsCopyTradeOrder({ network }));
      onClose();
      toastSuccess("Success", "Update successfully!");
    } catch (e) {
      console.error(e);
    }
  };

  const onSetDefault = () => {
    setSnipeSlippage(100);
    setIsEnableSnipeTip(true);
    setSnipeTipAmount("0.02");
    setSnipeGasPrice(1000);

    //
    setSlippage(40);
    setIsEnableTip(false);
    setTipAmount("");
    setGasPrice(750);

    // limit
    setLimitSlippage(50);
    setIsEnableLimitTip(true);
    setLimitTipAmount("0.02");
    setLimitGasPrice(800);

    //copytrade
    setCopyTradeSlippage(50);
    setIsEnableCopyTradeTip(true);
    setCopyTradeTipAmount("0.02");
    setCopyTradeGasPrice(800);
  };

  return (
    <ReactModal
      isOpen={isOpen}
      style={customStyles}
      ariaHideApp={false}
      onRequestClose={onClose}
      bodyOpenClassName="overflow-hidden"
    >
      <div className="tablet:max-w-[1080px] max-w-[calc(100vw-32px)] p-4">
        <div className="mb-1 flex justify-between">
          <div className="body-md-semibold-14">Settings</div>

          <div onClick={onClose} className="cursor-pointer">
            <CloseIcon className="h-4 w-4" />
          </div>
        </div>
        <div className="body-sm-regular-12 text-white-500 mb-5">
          The settings section lets you configure global preferences for all
          system functions
        </div>

        <div className="tablet:hidden border-white-100 mb-4 flex gap-3 border-b">
          {TABS.map((item, index) => {
            return (
              <div
                key={index}
                onClick={() => setTab(item.value)}
                className={`${
                  item.value === tab
                    ? "text-white-0 border-white-500 border-b"
                    : "text-white-500"
                } body-sm-regular-12 pb-1`}
              >
                {item.name}
              </div>
            );
          })}
        </div>

        <div className="tablet:grid-cols-4 grid grid-cols-1">
          {(tab === "snipe" || !isTablet) && (
            <div className="tablet:pr-4 tablet:border-r border-white-50">
              <div className="tablet:block text-white-800 body-sm-regular-12 mb-1 hidden">
                SNIPER
              </div>
              <div className="body-sm-regular-12 text-white-500 mb-4">
                These settings will be applied to all snipe functions
              </div>

              <div>
                <div className="mb-4">
                  <div className="action-xs-medium-12 text-white-700 mb-[8px] flex items-center gap-[4px]">
                    <SlippageIcon />
                    Slippage limit
                  </div>
                  <div className="border-white-50 flex items-center gap-[8px] rounded-[6px] border p-[8px]">
                    <div className="text-white-500 body-sm-regular-12">%</div>
                    <NumericFormat
                      placeholder="Enter slippage limit"
                      value={snipeSlippage}
                      onValueChange={({ value }) => setSnipeSlippage(+value)}
                      thousandSeparator=","
                      valueIsNumericString
                      decimalScale={2}
                      className="placeholder:text-white-300 body-md-semibold-14 text-neutral-0 w-full bg-transparent outline-none"
                    />
                  </div>
                </div>
                <div className="mb-4">
                  <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                    <GasIcon /> Gas Price
                  </div>

                  <div className="text-white-700 body-xs-regular-10 mb-1">
                    Enter gas price in MIST to estimate the gas fee for your
                    transaction. Min is 750 MIST
                  </div>

                  <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                    <div className="body-sm-regular-12 text-neutral-alpha-500">
                      MIST
                    </div>
                    <NumericFormat
                      value={snipeGasPrice ?? ""}
                      allowLeadingZeros
                      allowNegative={false}
                      thousandSeparator=","
                      className="body-md-semibold-14 w-full bg-transparent outline-none"
                      decimalScale={0}
                      onValueChange={({ floatValue }) => {
                        return setSnipeGasPrice(floatValue ? floatValue : 0);
                      }}
                    />
                  </div>

                  <div className="item-center flex justify-between">
                    <div className="body-xs-regular-10 text-white-700 mt-1">
                      Est Gas Fee:
                    </div>
                    <div className="text-white-1000 body-xs-medium-10 mt-1">
                      ~{formatNumber(maxGasFeeSnipe)} SUI
                    </div>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                      <CoinTip /> Tip Amount
                    </div>
                    <AppToggle
                      value={isEnableSnipeTip}
                      onChange={() => {
                        if (!isEnableSnipeTip) {
                          setSnipeTipAmount("0.02");
                        }
                        setIsEnableSnipeTip(!isEnableSnipeTip);
                      }}
                    />
                  </div>

                  <div className="body-xs-regular-10 text-white-700 mb-2">
                    The amount you send to validators to pick up your
                    transaction faster. Minimum is 0.02 SUI.
                  </div>

                  {isEnableSnipeTip && (
                    <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                      <div className="body-sm-regular-12 text-neutral-alpha-500">
                        <AppLogoNetwork
                          network={NETWORKS.SUI}
                          isBase
                          className="h-[12px] w-[12px]"
                        />
                      </div>
                      <NumericFormat
                        value={snipeTipAmount ?? ""}
                        allowLeadingZeros
                        allowNegative={false}
                        thousandSeparator=","
                        className="body-md-semibold-14 w-full bg-transparent outline-none"
                        decimalScale={6}
                        onValueChange={({ floatValue }) => {
                          return setSnipeTipAmount(floatValue);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {(tab === "buy-sell" || !isTablet) && (
            <div className="tablet:px-4">
              <div className="tablet:block text-white-800 body-sm-regular-12 mb-1 hidden">
                BUY/SELL
              </div>
              <div className="body-sm-regular-12 text-white-500 mb-4">
                These settings will be applied to the buy, sell, quick buy, dca
                functions
              </div>

              <div>
                <div className="mb-4">
                  <div className="action-xs-medium-12 text-white-700 mb-[8px] flex items-center gap-[4px]">
                    <SlippageIcon />
                    Slippage limit
                  </div>
                  <div className="border-white-50 flex items-center gap-[8px] rounded-[6px] border p-[8px]">
                    <div className="text-white-500 body-sm-regular-12">%</div>
                    <NumericFormat
                      placeholder="Enter slippage limit"
                      value={slippage}
                      onValueChange={({ value }) => setSlippage(+value)}
                      thousandSeparator=","
                      valueIsNumericString
                      decimalScale={2}
                      className="body-md-semibold-14 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
                    />
                  </div>
                </div>
                <div className="mb-4">
                  <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                    <GasIcon /> Gas Price
                  </div>

                  <div className="text-white-700 body-xs-regular-10 mb-1">
                    Enter gas price in MIST to estimate the gas fee for your
                    transaction. Min is 750 MIST
                  </div>

                  <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                    <div className="body-sm-regular-12 text-neutral-alpha-500">
                      MIST
                    </div>
                    <NumericFormat
                      value={gasPrice ?? ""}
                      allowLeadingZeros
                      allowNegative={false}
                      thousandSeparator=","
                      className="body-md-semibold-14 w-full bg-transparent outline-none"
                      decimalScale={0}
                      onValueChange={({ floatValue }) => {
                        return setGasPrice(floatValue ? floatValue : 0);
                      }}
                    />
                  </div>

                  <div className="item-center flex justify-between">
                    <div className="body-xs-regular-10 text-white-700 mt-1">
                      Est Gas Fee:
                    </div>
                    <div className="text-white-1000 body-xs-medium-10 mt-1">
                      ~{formatNumber(maxGasFeeBuySell)} SUI
                    </div>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                      <CoinTip /> Tip Amount
                    </div>
                    <AppToggle
                      value={isEnableTip}
                      onChange={() => {
                        if (!isEnableTip) {
                          setTipAmount("0.02");
                        }
                        setIsEnableTip(!isEnableTip);
                      }}
                    />
                  </div>

                  <div className="body-xs-regular-10 text-white-700 mb-2">
                    The amount you send to validators to pick up your
                    transaction faster. Minimum is 0.02 SUI.
                  </div>

                  {isEnableTip && (
                    <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                      <div className="body-sm-regular-12 text-neutral-alpha-500">
                        <AppLogoNetwork
                          network={NETWORKS.SUI}
                          isBase
                          className="h-[12px] w-[12px]"
                        />
                      </div>
                      <NumericFormat
                        value={tipAmount ?? ""}
                        allowLeadingZeros
                        allowNegative={false}
                        thousandSeparator=","
                        className="body-md-semibold-14 w-full bg-transparent outline-none"
                        decimalScale={6}
                        onValueChange={({ floatValue }) => {
                          return setTipAmount(floatValue ? floatValue : 0);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {(tab === "limit" || !isTablet) && (
            <div className="tablet:px-4 tablet:border-l border-white-50">
              <div className="tablet:block text-white-800 body-sm-regular-12 mb-1 hidden">
                LIMIT ORDERS
              </div>
              <div className="body-sm-regular-12 text-white-500 mb-4">
                These settings will be applied to all limit buy, limit sell,
                auto sell orders
              </div>

              <div>
                <div className="mb-4">
                  <div className="action-xs-medium-12 text-white-700 mb-[8px] flex items-center gap-[4px]">
                    <SlippageIcon />
                    Slippage limit
                  </div>
                  <div className="border-white-50 flex items-center gap-[8px] rounded-[6px] border p-[8px]">
                    <div className="text-white-500 body-sm-regular-12">%</div>
                    <NumericFormat
                      placeholder="Enter slippage limit"
                      value={limitSlippage}
                      onValueChange={({ value }) => setLimitSlippage(+value)}
                      thousandSeparator=","
                      valueIsNumericString
                      decimalScale={2}
                      className="body-md-semibold-14 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
                    />
                  </div>
                </div>
                <div className="mb-4">
                  <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                    <GasIcon /> Gas Price
                  </div>

                  <div className="text-white-700 body-xs-regular-10 mb-1">
                    Enter gas price in MIST to estimate the gas fee for your
                    transaction. Min is 750 MIST
                  </div>

                  <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                    <div className="body-sm-regular-12 text-neutral-alpha-500">
                      MIST
                    </div>
                    <NumericFormat
                      value={limitGasPrice ?? ""}
                      allowLeadingZeros
                      allowNegative={false}
                      thousandSeparator=","
                      className="body-md-semibold-14 w-full bg-transparent outline-none"
                      decimalScale={0}
                      onValueChange={({ floatValue }) => {
                        return setLimitGasPrice(floatValue ? floatValue : 0);
                      }}
                    />
                  </div>

                  <div className="item-center flex justify-between">
                    <div className="body-xs-regular-10 text-white-700 mt-1">
                      Est Gas Fee:
                    </div>
                    <div className="text-white-1000 body-xs-medium-10 mt-1">
                      ~{formatNumber(maxGasFeeLimit)} SUI
                    </div>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                      <CoinTip /> Tip Amount
                    </div>
                    <AppToggle
                      value={isEnableLimitTip}
                      onChange={() => {
                        if (!isEnableLimitTip) {
                          setLimitTipAmount("0.02");
                        }
                        setIsEnableLimitTip(!isEnableLimitTip);
                      }}
                    />
                  </div>

                  <div className="body-xs-regular-10 text-white-700 mb-2">
                    The amount you send to validators to pick up your
                    transaction faster. Minimum is 0.02 SUI.
                  </div>

                  {isEnableLimitTip && (
                    <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                      <div className="body-sm-regular-12 text-neutral-alpha-500">
                        <AppLogoNetwork
                          network={NETWORKS.SUI}
                          isBase
                          className="h-[12px] w-[12px]"
                        />
                      </div>
                      <NumericFormat
                        value={limitTipAmount ?? ""}
                        allowLeadingZeros
                        allowNegative={false}
                        thousandSeparator=","
                        className="body-md-semibold-14 w-full bg-transparent outline-none"
                        decimalScale={6}
                        onValueChange={({ floatValue }) => {
                          return setLimitTipAmount(floatValue ? floatValue : 0);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {(tab === "copy-trade" || !isTablet) && (
            <div className="tablet:pl-4 tablet:border-l border-white-50">
              <div className="tablet:block text-white-800 body-sm-regular-12 mb-1 hidden">
                COPY TRADE
              </div>
              <div className="body-sm-regular-12 text-white-500 mb-4">
                These settings will be applied to copy trade funcition
              </div>

              <div>
                <div className="mb-4">
                  <div className="action-xs-medium-12 text-white-700 mb-[8px] flex items-center gap-[4px]">
                    <SlippageIcon />
                    Slippage limit
                  </div>
                  <div className="border-white-50 flex items-center gap-[8px] rounded-[6px] border p-[8px]">
                    <div className="text-white-500 body-sm-regular-12">%</div>
                    <NumericFormat
                      placeholder="Enter slippage limit"
                      value={copyTradeSlippage}
                      onValueChange={({ value }) =>
                        setCopyTradeSlippage(+value)
                      }
                      thousandSeparator=","
                      valueIsNumericString
                      decimalScale={2}
                      className="body-md-semibold-14 placeholder:text-white-300 text-neutral-0 w-full bg-transparent outline-none"
                    />
                  </div>
                </div>
                <div className="mb-4">
                  <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                    <GasIcon /> Gas Price
                  </div>

                  <div className="text-white-700 body-xs-regular-10 mb-1">
                    Enter gas price in MIST to estimate the gas fee for your
                    transaction. Min is 750 MIST
                  </div>

                  <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                    <div className="body-sm-regular-12 text-neutral-alpha-500">
                      MIST
                    </div>
                    <NumericFormat
                      value={copyTradeGasPrice ?? ""}
                      allowLeadingZeros
                      allowNegative={false}
                      thousandSeparator=","
                      className="body-md-semibold-14 w-full bg-transparent outline-none"
                      decimalScale={0}
                      onValueChange={({ floatValue }) => {
                        return setCopyTradeGasPrice(
                          floatValue ? floatValue : 0
                        );
                      }}
                    />
                  </div>

                  <div className="item-center flex justify-between">
                    <div className="body-xs-regular-10 text-white-700 mt-1">
                      Est Gas Fee:
                    </div>
                    <div className="text-white-1000 body-xs-medium-10 mt-1">
                      ~{formatNumber(maxGasFeeCopyTrade)} SUI
                    </div>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                      <CoinTip /> Tip Amount
                    </div>
                    <AppToggle
                      value={isEnableCopyTradeTip}
                      onChange={() => {
                        if (!isEnableCopyTradeTip) {
                          setCopyTradeTipAmount("0.02");
                        }
                        setIsEnableCopyTradeTip(!isEnableCopyTradeTip);
                      }}
                    />
                  </div>

                  <div className="body-xs-regular-10 text-white-700 mb-2">
                    The amount you send to validators to pick up your
                    transaction faster. Minimum is 0.02 SUI.
                  </div>

                  {isEnableCopyTradeTip && (
                    <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                      <div className="body-sm-regular-12 text-neutral-alpha-500">
                        <AppLogoNetwork
                          network={NETWORKS.SUI}
                          isBase
                          className="h-[12px] w-[12px]"
                        />
                      </div>
                      <NumericFormat
                        value={copyTradeTipAmount ?? ""}
                        allowLeadingZeros
                        allowNegative={false}
                        thousandSeparator=","
                        className="body-md-semibold-14 w-full bg-transparent outline-none"
                        decimalScale={6}
                        onValueChange={({ floatValue }) => {
                          return setCopyTradeTipAmount(
                            floatValue ? floatValue : 0
                          );
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="mt-5 flex items-center justify-center gap-3">
          <AppButton
            variant="secondary"
            size="large"
            className="min-w-[134px]"
            onClick={onSetDefault}
          >
            Default
          </AppButton>
          <AppButton variant="buy" size="large" onClick={onApply}>
            Save Settings
          </AppButton>
        </div>
      </div>
    </ReactModal>
  );
};

export default ModalSettingsOrder;
