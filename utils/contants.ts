import { OrderFormType } from "@/enums";

export const MOONBAGS_API_URL = "https://api2.moonbags.io/api/v1";

export const CATEGORY_MARKER = {
  SHRIMP: "shrimp",
  WHALE: "whale",
  DOLPHIN: "dolphin",
  PLANKTON: "plankton",
  FISH: "fish",
};

export const NETWORKS = {
  SUI: "sui",
};

export const DATE_TYPE = {
  DATE: "Date",
  AGE: "Age",
};

export const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

export const SUI_TOKEN_ADDRESS_FULL =
  "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI";
export const SUI_TOKEN_ADDRESS_SHORT = "0x2::sui::SUI";

export const USDC_ADDRESS =
  "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC";
export const USDC_DECIMALS = 6;

export const SUI_DECIMALS = 9;

export const LOCK_TRUST_ME_BRO = "246153846153893490";
export const LOCK_TRUST_ME_3000 = "505263157894803323";
export const LOW_LIQUIDITY = 5000;
export const LOW_LIQUIDITY_FUNZONE = 1000;

export const SUI_TOKEN_METADATA = {
  address: SUI_TOKEN_ADDRESS_SHORT,
  decimals: SUI_DECIMALS,
  symbol: "SUI",
  name: "SUI",
};

export const OPTIONS_ORDER_TYPE = [
  {
    name: "Market",
    value: OrderFormType.MARKET,
  },
  {
    name: "Limit",
    value: OrderFormType.LIMIT,
  },
  {
    name: "DCA",
    value: OrderFormType.DCA,
  },
  {
    name: "Migration",
    value: OrderFormType.MIGRATION,
  },
];

export const LOGIN_METHODS = {
  TELEGRAM: "telegram",
  PRIVY: "privy",
  WALLET: "wallet",
};
