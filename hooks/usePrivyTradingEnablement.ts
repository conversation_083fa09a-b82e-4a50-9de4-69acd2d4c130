import { useCallback, useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import Storage from "@/libs/storage";
import { LOGIN_METHODS } from "@/utils/contants";

export const usePrivyTradingEnablement = () => {
  const { user: privyUser } = usePrivy();

  const isPrivyUser = Storage.getLoginMethod() === LOGIN_METHODS.PRIVY;

  const getAllSuiWallets = useCallback(() => {
    return (
      (privyUser?.linkedAccounts?.filter(
        (account: any) =>
          account.type === "wallet" && account.chainType === "sui"
      ) as any[]) || []
    );
  }, [privyUser?.linkedAccounts]);

  const hasPrivyWallet = getAllSuiWallets().length > 0;

  return {
    isPrivyUser,
    hasPrivyWallet,
    getAllSuiWallets,
  };
};
