"use client";

import BigN<PERSON>ber from "bignumber.js";
import { useCallback, useRef } from "react";
import { useSelector } from "react-redux";
import { useRaidenxWallet } from "@/hooks/useRaidenxWallet";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastInfo, toastWarning } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair } from "@/types";
import {
  convertMistToDec,
  filterParams,
  isZero,
  minusBN,
  toStringBN,
} from "@/utils/helper";
import {
  simulateBuyExactIn,
  simulateSellExactIn,
  TDexPool,
} from "@/utils/simulates";
import { roundNumber } from "@/utils/format";
import config from "@/config";
import { isBuyBySuiToken } from "@/utils/pair";
import {
  getCoinBalanceOnchain,
  getCoinDecimalsOnchain,
} from "@/utils/suiClient";

const MIN_TOKEN_BALANCE_TO_SELL = "0.00000000000001";

export const useOrder = () => {
  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );
  const settingsLimitOrder = useSelector(
    (state: RootState) => state.user.settingsLimitOrder
  );

  const balances = useSelector((state: RootState) => state.user.balances);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const network = useSelector((state: RootState) => state.user.network);
  const isSubmitOrder = useRef<boolean>(false);

  const { activeWalletAddresses, activeTotalSuiBalance } = useRaidenxWallet();

  const MIN_GAS_FEE = config.minGasFee;

  const getMinSuiAmountToBuy = () => {
    return new BigNumber(settingsQuickOrder?.tipAmount || 0)
      .plus(MIN_GAS_FEE || 0.1)
      .toString(); // 0.1 to pay gas
  };

  const validateSuiBalanceForGasFee = () => {
    const minSuiAmount = getMinSuiAmountToBuy();
    if (new BigNumber(activeTotalSuiBalance).comparedTo(minSuiAmount) <= 0) {
      throw Error(
        `Insufficient sui balance to cover gas fee (min ${minSuiAmount})`
      );
    }
  };

  const getAvailableAddressesWithEnoughBalance = async (
    amount: string,
    tokenAddress: string,
    tokenDecimals: number
  ) => {
    const availableAddresses = [];
    for (const activeWallet of activeWalletAddresses || []) {
      const currentBalanceOnchain = await getCoinBalanceOnchain(
        activeWallet,
        tokenAddress
      );

      const currentBalance = convertMistToDec(
        currentBalanceOnchain,
        tokenDecimals
      );
      if (new BigNumber(currentBalance).comparedTo(amount) >= 0) {
        availableAddresses.push({
          address: activeWallet,
          balance: currentBalance,
        });
      }
    }
    return availableAddresses;
  };

  const getRealAmountCanBuy = useCallback(
    (pair: TPair, buyAmount: string, isBuyBySuiToken = false) => {
      if (isBuyBySuiToken) {
        return BigNumber.min(
          buyAmount,
          minusBN(activeTotalSuiBalance, getMinSuiAmountToBuy())
        ).toString();
      }
      return buyAmount;
    },
    [activeTotalSuiBalance, balances, wallets]
  );

  const quickBuy = useCallback(
    async (
      autoSellSettings: any,
      pair: TPair,
      buyAmount: string,
      buyByToken: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        buyAmount,
        buyByToken || pair.tokenQuote.address,
        await getCoinDecimalsOnchain(buyByToken || pair.tokenQuote.address)
      );
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }
        validateSuiBalanceForGasFee();
        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }
        if (isSubmitOrder.current) {
          toastWarning(
            "Warning",
            "Please wait for the previous order to complete!"
          );
          return;
        }
        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          isBuyBySuiToken(buyByToken)
        );
        isSubmitOrder.current = true;

        await rf.getRequest("NewOrderRequest").createOrderQuickOrder(
          pair.network,
          filterParams({
            autoSellSettings: !!autoSellSettings
              ? {
                  ...autoSellSettings,
                  triggers: autoSellSettings?.triggers?.map((trigger: any) => ({
                    ...trigger,
                    priceChangePercent: Number(trigger?.priceChangePercent),
                    sellPercent: Number(trigger?.sellPercent),
                  })),
                }
              : null,
            amount: toStringBN(maxAmountCanBuy),
            orderSetting: {
              tipAmount: toStringBN(settingsQuickOrder?.tipAmount || 0),
              slippage: Number(settingsQuickOrder?.slippage || 10),
              gasPrice: +settingsQuickOrder.gasPrice,
            },
            type: "BUY",
            typeAmount: "FIX_AMOUNT",
            pairId: pair?.pairId,
            tokenOutAddress: pair?.tokenBase?.address,
            tokenInAddress: buyByToken,
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );
        toastInfo("Submitting", "Submitting Order!");
        if (typeof onSuccess === "function") {
          onSuccess();
        }
      } catch (e: any) {
        console.error("buyOrder error", e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [
      settingsQuickOrder,
      activeTotalSuiBalance,
      balances,
      activeWalletAddresses,
      wallets,
    ]
  );

  const buyLimit = useCallback(
    async (
      autoSellSettings: any,
      pair: TPair,
      buyAmount: string,
      targetPriceUsd: string,
      buyByToken?: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }
        const walletEnoughBalance =
          await getAvailableAddressesWithEnoughBalance(
            buyAmount,
            buyByToken || pair.tokenQuote.address,
            await getCoinDecimalsOnchain(buyByToken || pair.tokenQuote.address)
          );
        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }
        await rf.getRequest("NewOrderRequest").createOrderLimitOrder(
          pair.network,
          filterParams({
            autoSellSettings,
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            tokenInAddress: buyByToken,
            tokenOutAddress: pair?.tokenBase?.address,
            pairId: pair?.pairId,
            type: "BUY",
            typeAmount: "FIX_AMOUNT",
            amount: toStringBN(buyAmount),
            targetPriceUsd,
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settingsLimitOrder, activeWalletAddresses]
  );

  const buyDCA = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      interval: number | string,
      tokenPriceRange: any,
      repeat: number,
      buyByToken: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }

        if (!interval) {
          throw Error("Please input interval!");
        }

        if (!repeat) {
          throw Error("Please input amount order!");
        }

        if (
          tokenPriceRange &&
          !!tokenPriceRange?.min &&
          !!tokenPriceRange?.max &&
          new BigNumber(tokenPriceRange?.min).gte(tokenPriceRange?.max)
        ) {
          throw Error(
            "Minimum market cap must be less than maximum market cap!"
          );
        }

        validateSuiBalanceForGasFee();

        const walletEnoughBalance =
          await getAvailableAddressesWithEnoughBalance(
            buyAmount,
            buyByToken || pair.tokenQuote.address,
            await getCoinDecimalsOnchain(buyByToken || pair.tokenQuote.address)
          );

        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }

        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          isBuyBySuiToken(buyByToken)
        );

        await rf.getRequest("NewOrderRequest").createOrderDCAOrder(
          pair.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            interval,
            repeat,
            tokenPriceRange,
            tokenInAddress: buyByToken,
            tokenOutAddress: pair?.tokenBase?.address,
            pairId: pair?.pairId,
            type: "BUY",
            typeAmount: "FIX_AMOUNT",
            amount: toStringBN(maxAmountCanBuy),
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settingsLimitOrder, activeWalletAddresses]
  );

  const buyMigration = useCallback(
    async (
      autoSellSettings: any,
      pair: TPair,
      buyAmount: string,
      buyByToken: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        buyAmount,
        buyByToken || pair.tokenQuote.address,
        await getCoinDecimalsOnchain(buyByToken || pair.tokenQuote.address)
      );
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }
        validateSuiBalanceForGasFee();

        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }

        if (isSubmitOrder.current) {
          toastWarning(
            "Warning",
            "Please wait for the previous order to complete!"
          );
          return;
        }

        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          isBuyBySuiToken(buyByToken)
        );

        isSubmitOrder.current = true;

        await rf.getRequest("NewOrderRequest").createOrderMigrationOrder(
          pair.network,
          filterParams({
            autoSellSettings: !!autoSellSettings
              ? {
                  ...autoSellSettings,
                  triggers: autoSellSettings?.triggers?.map((trigger: any) => ({
                    ...trigger,
                    priceChangePercent: Number(trigger?.priceChangePercent),
                    sellPercent: Number(trigger?.sellPercent),
                  })),
                }
              : null,
            amount: toStringBN(maxAmountCanBuy),
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            type: "BUY",
            typeAmount: "FIX_AMOUNT",
            pairId: pair?.pairId,
            tokenOutAddress: pair?.tokenBase?.address,
            tokenInAddress: buyByToken,
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );
        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        if (typeof onSuccess === "function") {
          onSuccess();
        }
      } catch (e: any) {
        console.error("buyOrder error", e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [
      settingsLimitOrder,
      activeTotalSuiBalance,
      balances,
      activeWalletAddresses,
      wallets,
    ]
  );

  const estimateQuickBuy = useCallback(
    async (pair: TPair, buyAmount: string, tokenQuoteAddress: string) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        buyAmount,
        tokenQuoteAddress,
        pair.tokenQuote.decimals
      );

      try {
        if (!buyAmount) {
          return;
        }
        validateSuiBalanceForGasFee();
        if (!walletEnoughBalance.length) {
          return;
        }
        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          isBuyBySuiToken(tokenQuoteAddress)
        );

        const pool: TDexPool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        const addresses = walletEnoughBalance.map((wallet) => wallet.address);
        const amountOutList = await Promise.all(
          addresses.map(async (walletAddress) => {
            try {
              return await simulateBuyExactIn(
                walletAddress,
                new BigNumber(
                  roundNumber(
                    maxAmountCanBuy,
                    BigNumber.ROUND_DOWN,
                    pair?.tokenQuote?.decimals
                  )
                ),
                pair?.tokenQuote,
                pair?.tokenBase,
                pool,
                isBuyBySuiToken(tokenQuoteAddress),
                pair.isXQuoteToken,
                pair.feeTier
              );
            } catch (e: any) {
              console.error(e);
              return "0";
            }
          })
        );
        const totalAmountOut = amountOutList.reduce(
          (prev, amountOut) => new BigNumber(prev).plus(amountOut).toString(),
          new BigNumber(0)
        );

        return totalAmountOut.toString();
      } catch (e: any) {
        console.error(e);
        return "";
      }
    },
    [
      settingsQuickOrder,
      activeTotalSuiBalance,
      balances,
      activeWalletAddresses,
      wallets,
    ]
  );

  const estimateQuickSell = useCallback(
    async (pair: TPair, sellPercent: number, walletAddresses?: string[]) => {
      if (!pair?.network || !pair?.pairId) return;
      if (isZero(sellPercent)) {
        return;
      }

      const pool: TDexPool = {
        dex: pair.dex.dex as any,
        objectId: pair.poolId,
      };

      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (!walletEnoughBalance.length) {
        return;
      }

      const addresses = walletEnoughBalance.map((wallet) => wallet.address);

      const amountOutList = await Promise.all(
        addresses.map(async (walletAddress) => {
          try {
            return await simulateSellExactIn(
              walletAddress,
              sellPercent,
              pair?.tokenBase,
              pair?.tokenQuote,
              pool,
              pair?.feeTier,
              pair?.isXQuoteToken
            );
          } catch (e: any) {
            console.error(e);
            return "0";
          }
        })
      );

      const totalAmountOut = amountOutList.reduce(
        (prev, amountOut) => prev.plus(amountOut),
        new BigNumber(0)
      );

      return totalAmountOut.toString();
    },
    [settingsQuickOrder, activeWalletAddresses, balances, wallets]
  );

  const quickSell = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (isZero(sellPercent)) {
        return;
      }
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      if (isSubmitOrder.current) {
        toastWarning(
          "Warning",
          "Please wait for the previous order to complete!"
        );
        return;
      }
      isSubmitOrder.current = true;
      try {
        await rf.getRequest("NewOrderRequest").createOrderQuickOrder(
          pair?.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsQuickOrder?.tipAmount || 0),
              slippage: Number(settingsQuickOrder?.slippage || 10),
              gasPrice: +settingsQuickOrder.gasPrice,
            },
            pairId: pair?.pairId,
            amount: toStringBN(sellPercent),
            type: "SELL",
            typeAmount: "PERCENT",
            tokenInAddress: pair?.tokenBase?.address,
            tokenOutAddress: pair?.tokenQuote?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [settingsQuickOrder, activeWalletAddresses, balances, wallets]
  );

  const sellLimit = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      targetPriceUsd: string,
      targetMcPercent: number | null,
      targetPricePercent: number | null,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      try {
        await rf.getRequest("NewOrderRequest").createOrderLimitOrder(
          pair?.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            pairId: pair?.pairId,
            amount: toStringBN(sellPercent),
            type: "SELL",
            typeAmount: "PERCENT",
            tokenInAddress: pair?.tokenBase?.address,
            tokenOutAddress: pair?.tokenQuote?.address,
            targetPriceUsd,
            targetMcPercent,
            targetPricePercent,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settingsLimitOrder, activeWalletAddresses]
  );

  const sellDCA = useCallback(
    async (
      pair: TPair,
      interval: number | string,
      repeat: number,
      tokenPriceRange: any,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }

      if (!interval) {
        toastError("Error", "Please input interval!");
        return;
      }

      if (!repeat) {
        toastError("Error", "Please input amount order!");
        return;
      }

      if (
        tokenPriceRange &&
        tokenPriceRange?.min &&
        tokenPriceRange?.max &&
        new BigNumber(tokenPriceRange?.min).gte(tokenPriceRange?.max)
      ) {
        toastError(
          "Error",
          "Minimum market cap must be less than maximum market cap!"
        );
        return;
      }

      try {
        await rf.getRequest("NewOrderRequest").createOrderDCAOrder(
          pair.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            pairId: pair?.pairId,
            type: "SELL",
            amount: toStringBN(sellPercent),
            typeAmount: "PERCENT",
            tokenInAddress: pair?.tokenBase?.address,
            tokenOutAddress: pair?.tokenQuote?.address,
            interval,
            repeat,
            tokenPriceRange,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settingsLimitOrder, activeWalletAddresses]
  );

  const sellMigration = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (isZero(sellPercent)) {
        return;
      }
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      if (isSubmitOrder.current) {
        toastWarning(
          "Warning",
          "Please wait for the previous order to complete!"
        );
        return;
      }
      isSubmitOrder.current = true;
      try {
        await rf.getRequest("NewOrderRequest").createOrderMigrationOrder(
          pair?.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            pairId: pair?.pairId,
            amount: toStringBN(sellPercent),
            type: "SELL",
            typeAmount: "PERCENT",
            tokenInAddress: pair?.tokenBase?.address,
            tokenOutAddress: pair?.tokenQuote?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [settingsLimitOrder, activeWalletAddresses, balances, wallets]
  );

  const devSell = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address,
        pair.tokenBase.decimals
      );
      if (isZero(sellPercent)) {
        return;
      }
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      if (isSubmitOrder.current) {
        toastWarning(
          "Warning",
          "Please wait for the previous order to complete!"
        );
        return;
      }
      isSubmitOrder.current = true;
      try {
        await rf.getRequest("NewOrderRequest").createOrderDevSellOrder(
          pair?.network,
          filterParams({
            orderSetting: {
              tipAmount: toStringBN(settingsLimitOrder?.tipAmount || 0),
              slippage: Number(settingsLimitOrder?.slippage || 10),
              gasPrice: +settingsLimitOrder.gasPrice,
            },
            pairId: pair?.pairId,
            amount: toStringBN(sellPercent),
            type: "SELL",
            typeAmount: "PERCENT",
            tokenInAddress: pair?.tokenBase?.address,
            tokenOutAddress: pair?.tokenQuote?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [settingsLimitOrder, activeWalletAddresses, balances, wallets]
  );

  const closePosition = useCallback(
    async (
      sellPercent: number,
      tokenAddress: string,
      walletAddress: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!sellPercent) {
          throw Error("Please input percent amount!");
        }
        if (isSubmitOrder.current) {
          toastWarning(
            "Warning",
            "Please wait for the previous order to complete!"
          );
          return;
        }
        isSubmitOrder.current = true;
        await rf.getRequest("NewOrderRequest").closePosition(
          network,
          filterParams({
            // orderSetting: {
            //   tipAmount: settingsQuickOrder?.tipAmount,
            //   slippage: settingsQuickOrder?.slippage,
            //   gasPrice: +settingsQuickOrder.gasPrice,
            // },
            sellPercent,
            tokenAddress: tokenAddress,
            walletAddress,
          })
        );

        toastInfo("Success", "Close position successfully!");
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [network, settingsQuickOrder]
  );

  return {
    quickBuy,
    buyLimit,
    buyDCA,
    buyMigration,
    estimateQuickBuy,
    quickSell,
    estimateQuickSell,
    sellLimit,
    sellDCA,
    sellMigration,
    devSell,
    closePosition,
  };
};
