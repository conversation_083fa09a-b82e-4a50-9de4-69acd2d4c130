import { TDex, TPairToken } from "@/types";
import { EDex, MEME_DEXES } from "@/enums";
import BigNumber from "bignumber.js";
import { LOCK_TRUST_ME_3000, LOCK_TRUST_ME_BRO } from "@/utils/contants";
import { dividedBN, multipliedBN } from "@/utils/helper";
import { useMemo } from "react";

export const useAudit = ({
  tokenBase,
  dex,
  lpBurned,
  lpSupply,
}: {
  tokenBase: TPairToken;
  dex: TDex;
  lpBurned: string | number | null;
  lpSupply: string | number | null;
}) => {
  const issueTop10Holder =
    !!+tokenBase?.top10HolderPercent && tokenBase?.top10HolderPercent >= 15;

  const issueDevBalance =
    !!tokenBase?.deployerBalancePercent &&
    tokenBase?.deployerBalancePercent >= 10;

  const isFlowXDexAndSevenKDex =
    dex?.dex && [EDex.FLOWX, EDex.SEVENKFUN].includes(dex?.dex as EDex);

  const isSevenKDex = dex?.dex && [EDex.SEVENKFUN].includes(dex?.dex as EDex);

  const isMemeDex = dex && MEME_DEXES.includes(dex.dex as EDex);

  const issueLockedAmount =
    isFlowXDexAndSevenKDex &&
    tokenBase?.lockTimestamp &&
    +tokenBase?.lockTimestamp > new Date().valueOf() &&
    ((tokenBase?.lockAmount === LOCK_TRUST_ME_BRO &&
      new BigNumber(tokenBase?.lockedAmount || 0).lte(LOCK_TRUST_ME_BRO)) ||
      (tokenBase?.lockAmount === LOCK_TRUST_ME_3000 &&
        new BigNumber(tokenBase?.lockedAmount || 0).lte(LOCK_TRUST_ME_3000)));

  const lpBurnedPercent = useMemo(() => {
    if (!lpSupply) return 0;
    return multipliedBN(dividedBN(lpBurned || 0, lpSupply || 0), 100);
  }, [lpSupply, lpBurned]);

  const issueLpBurned =
    !isMemeDex &&
    (!+lpBurnedPercent ||
      (!!+lpBurnedPercent &&
        new BigNumber(lpBurnedPercent).comparedTo(65) < 0));

  return {
    isMemeDex,
    issueLpBurned,
    issueTop10Holder,
    issueLockedAmount,
    issueDevBalance,
    isFlowXDexAndSevenKDex,
    isSevenKDex,
    lpBurnedPercent,
  };
};
