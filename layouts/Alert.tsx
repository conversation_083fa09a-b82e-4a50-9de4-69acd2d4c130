"use client";

import { default as React, useEffect, useState } from "react";
import {
  TimerIcon,
  TrashIcon,
  EditIcon,
  CloseIcon,
  ReloadIcon,
} from "@/assets/icons";
import {
  AppAvatarToken,
  AppButton,
  AppDatePicker,
  AppDropdown,
  AppLogoNetwork,
  AppPopover,
} from "../components";
import rf from "@/services/RequestFactory";
import { formatUnixTimestamp } from "@/utils/format";
import { toastError, toastSuccess } from "@/libs/toast";
import { TAlert, TPair } from "@/types";
import { CONDITION_ALERT, TRIGGER_TYPE_ALERT } from "@/enums";
import { NumericFormat } from "react-number-format";
import moment from "moment";
import { filterParams } from "@/utils/helper";
import { NETWORKS, SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { useMediaQuery } from "react-responsive";
import AppDrawer from "@/components/AppDrawer";
import clsx from "clsx";
import { getValue } from "@/utils/alert";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";

export const _renderLogoToken = (address: string) => {
  if (address === SUI_TOKEN_ADDRESS_FULL) {
    return (
      <AppLogoNetwork
        network={NETWORKS.SUI}
        className={`w-[${14}px] h-[${14}px]`}
        isBase
      />
    );
  }

  return <AppAvatarToken size={14} />;
};

const FormEditAlert = ({
  onCloseEdit,
  fetchData,
  alert,
}: {
  pair?: TPair;
  onCloseEdit: () => void;
  fetchData: () => void;
  alert: TAlert;
}) => {
  const [type, setType] = useState<string>(TRIGGER_TYPE_ALERT.GOES_OVER);
  const [condition, setCondition] = useState<string>(CONDITION_ALERT.PRICE_USD);
  const [name, setName] = useState<string>("");
  const [value, setValue] = useState<any>("");
  const [time, setTime] = useState<Date | null>(
    new Date(Date.now() + 3600 * 1000 * 24)
  );

  const CONDITION_OPTIONS = [
    {
      name: "Price in USD",
      value: CONDITION_ALERT.PRICE_USD,
    },
    {
      name: `Price in ${alert?.tokenQuote?.symbol}`,
      value: CONDITION_ALERT.PRICE_QUOTE,
    },
    {
      name: "Market Cap",
      value: CONDITION_ALERT.MARKET_CAP,
    },
  ];

  const TYPE_OPTIONS = [
    {
      name: "Goes over",
      value: TRIGGER_TYPE_ALERT.GOES_OVER,
    },
    {
      name: "Goes under",
      value: TRIGGER_TYPE_ALERT.GOES_UNDER,
    },
    condition === CONDITION_ALERT.PRICE_USD && {
      name: "Goes up more than",
      value: TRIGGER_TYPE_ALERT.GOES_UP_MORE_THAN,
    },
    condition === CONDITION_ALERT.PRICE_USD && {
      name: "Goes down more than",
      value: TRIGGER_TYPE_ALERT.GOES_DOWN_MORE_THAN,
    },
  ].filter(Boolean) as any[];

  const targetValue = () => {
    if (alert.condition === CONDITION_ALERT.PRICE_QUOTE)
      return alert.metadata.targetPrice;

    if (alert.condition === CONDITION_ALERT.PRICE_USD) {
      if (
        alert.metadata.triggerType === TRIGGER_TYPE_ALERT.GOES_UP_MORE_THAN ||
        alert.metadata.triggerType === TRIGGER_TYPE_ALERT.GOES_DOWN_MORE_THAN
      ) {
        return alert.metadata.changePercent;
      }
      return alert.metadata.targetPriceUsd;
    }

    return alert.metadata.targetMc;
  };

  //set default value form
  useEffect(() => {
    setCondition(alert.condition);
    setType(alert.metadata.triggerType);
    setName(alert.alertName);
    setTime(new Date(alert.expiredAt * 1000));
    setValue(targetValue());
  }, [alert]);

  //set default when change condition & trigger type
  useEffect(() => {
    if (condition === CONDITION_ALERT.PRICE_USD) {
      if (
        type === TRIGGER_TYPE_ALERT.GOES_DOWN_MORE_THAN ||
        type === TRIGGER_TYPE_ALERT.GOES_UP_MORE_THAN
      ) {
        setValue(alert.metadata.changePercent || "");
        return;
      } else {
        setValue(alert.metadata.targetPriceUsd || "");
        return;
      }
    }

    if (condition === CONDITION_ALERT.PRICE_QUOTE) {
      setValue(alert.metadata.targetPrice || "");
      if (
        type !== TRIGGER_TYPE_ALERT.GOES_OVER &&
        type !== TRIGGER_TYPE_ALERT.GOES_UNDER
      ) {
        setType(TRIGGER_TYPE_ALERT.GOES_OVER);
      }
      return;
    }

    if (condition === CONDITION_ALERT.MARKET_CAP) {
      setValue(alert.metadata.targetMc || "");
      if (
        type !== TRIGGER_TYPE_ALERT.GOES_OVER &&
        type !== TRIGGER_TYPE_ALERT.GOES_UNDER
      ) {
        setType(TRIGGER_TYPE_ALERT.GOES_OVER);
      }
      return;
    }
  }, [condition, type]);

  const getTargetPriceUsd = () => {
    if (condition === CONDITION_ALERT.PRICE_USD) {
      if (
        type === TRIGGER_TYPE_ALERT.GOES_OVER ||
        type === TRIGGER_TYPE_ALERT.GOES_UNDER
      ) {
        return value?.toString();
      }
      return "";
    }
    return "";
  };

  const getChangePercent = () => {
    if (condition === CONDITION_ALERT.PRICE_USD) {
      if (
        type === TRIGGER_TYPE_ALERT.GOES_DOWN_MORE_THAN ||
        type === TRIGGER_TYPE_ALERT.GOES_UP_MORE_THAN
      ) {
        return +value;
      }
      return "";
    }
    return "";
  };

  const editAlert = async () => {
    const getValue = (key: string) => {
      if (condition === key) {
        return value?.toString();
      }
      return "";
    };

    const params = {
      poolId: alert.poolId,
      condition: condition,
      alertName: name,
      expiredAt: moment(time).unix(),
      triggerType: type,
      targetPrice: getValue(CONDITION_ALERT.PRICE_QUOTE),
      targetPriceUsd: getTargetPriceUsd(),
      targetMc: getValue(CONDITION_ALERT.MARKET_CAP),
      changePercent: getChangePercent(),
    };

    try {
      await rf
        .getRequest("AlertRequest")
        .editAlert(alert.hash, filterParams(params));
      toastSuccess("Successfully!", `Alert updated successfully`);
      onCloseEdit();
      fetchData();
    } catch (e: any) {
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const _renderUnitInput = () => {
    if (
      type === TRIGGER_TYPE_ALERT.GOES_UNDER ||
      type === TRIGGER_TYPE_ALERT.GOES_OVER
    ) {
      if (condition === CONDITION_ALERT.PRICE_QUOTE) {
        return _renderLogoToken(alert?.tokenQuote?.address);
      }
      return "$";
    }
    return "";
  };

  const _renderUnit = () => {
    if (
      type === TRIGGER_TYPE_ALERT.GOES_UNDER ||
      type === TRIGGER_TYPE_ALERT.GOES_OVER
    ) {
      if (condition === CONDITION_ALERT.PRICE_QUOTE) {
        return _renderLogoToken(alert?.tokenQuote?.address);
      }
      return "$";
    }
    return "%";
  };

  const disabledSubmitButton =
    !value || time === null || moment().isAfter(time);

  return (
    <div className="pl-[40px]">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <div className="body-sm-medium-12 text-white-700">Condition</div>
          <div className="flex flex-grow gap-2">
            <AppDropdown
              options={CONDITION_OPTIONS}
              onSelect={(value) => {
                setCondition(value);
              }}
              className="border-white-100 bg-white-50 h-full max-h-[34px] min-w-[140px] flex-1 gap-2 border px-2 py-[6px] md:min-w-[217px]"
              value={condition}
              hideTick
            />

            <AppDropdown
              options={TYPE_OPTIONS}
              onSelect={(value) => {
                setType(value);
              }}
              className="border-white-100 bg-white-50 h-full max-h-[34px] min-w-[163px] flex-1 gap-2 border px-2 py-[6px]"
              value={type}
              hideTick
            />
          </div>
          <div className="flex w-full gap-2">
            <div className="body-sm-regular-12 border-white-100 bg-white-50 flex flex-1 items-center gap-2 rounded-[6px] border p-2">
              {_renderUnitInput()}

              <NumericFormat
                value={value}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                decimalScale={
                  condition === CONDITION_ALERT.PRICE_QUOTE
                    ? alert?.tokenQuote?.decimals
                    : 8
                }
                className="body-sm-regular-12 flex-1 bg-transparent outline-none"
                onValueChange={({ floatValue }) => {
                  return setValue(floatValue);
                }}
              />
            </div>

            <div className="body-sm-regular-12 text-white-300 border-white-100 bg-white-50 flex w-[50px] items-center justify-center rounded-[6px] border p-2">
              {_renderUnit()}
            </div>
          </div>
        </div>
        <div>
          <div className="body-sm-medium-12 text-white-700 mb-2">
            Expiration
          </div>
          <AppDatePicker value={time} setValue={setTime} />
        </div>

        <div>
          <div className="body-sm-medium-12 text-white-700 mb-2">
            Alert name
          </div>
          <input
            className="body-sm-regular-12 placeholder:text-white-300 border-white-100 bg-white-50 w-full rounded-[6px] border p-2 outline-none"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Create your alert name (optional)"
          />
        </div>

        <div className="flex justify-end gap-2">
          <AppButton onClick={editAlert} disabled={disabledSubmitButton}>
            Save changes
          </AppButton>
          <AppButton
            variant="outline"
            className="w-[80px]"
            onClick={onCloseEdit}
          >
            Cancel
          </AppButton>
        </div>
      </div>
    </div>
  );
};

const AlertItem = ({
  alert,
  fetchData,
  type,
}: {
  alert: TAlert;
  type: string;
  fetchData: () => void;
}) => {
  const [isShowEdit, setIsShowEdit] = useState<boolean>(false);

  const onDeleteAlert = async () => {
    try {
      await rf.getRequest("AlertRequest").deleteAlert(alert.hash);
      toastSuccess("Successfully!", `Delete alert successfully`);
      fetchData();
    } catch (e: any) {
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  return (
    <div
      className={`border-white-50 flex flex-col gap-3 border-b border-dashed p-3 ${
        isShowEdit ? "bg-white-50" : ""
      }`}
    >
      <div className="flex gap-2">
        <AppAvatarToken />
        <div className="flex flex-1 flex-col gap-[2px]">
          <div>
            {alert?.tokenBase.symbol} / {alert.tokenQuote.symbol}
            {alert?.alertName ? ` (${alert?.alertName})` : ""}
          </div>
          <div className="body-sm-regular-12 text-white-700">
            Alert me when{" "}
            <span className="text-brand-600 body-sm-medium-12">
              {alert?.tokenBase.symbol}{" "}
              {alert.condition === CONDITION_ALERT.MARKET_CAP
                ? "market cap"
                : "price"}{" "}
              {alert?.metadata.triggerType.replace("_", " ")} {getValue(alert)}
            </span>
          </div>
          <div className="body-xs-regular-10 text-white-500">
            {formatUnixTimestamp(alert.expiredAt * 1000, "DD/MM/YYYY HH:mm:ss")}
          </div>
        </div>

        {!isShowEdit && (
          <div className="flex h-max gap-2">
            {type === "active" && (
              <AppButton
                variant="secondary"
                size="small"
                onClick={() => setIsShowEdit(true)}
              >
                <EditIcon className="h-4 w-4" />
              </AppButton>
            )}
            <AppButton variant="secondary" size="small" onClick={onDeleteAlert}>
              <TrashIcon className="h-4 w-4" />
            </AppButton>
          </div>
        )}
      </div>

      {isShowEdit && (
        <FormEditAlert
          alert={alert}
          onCloseEdit={() => setIsShowEdit(false)}
          fetchData={fetchData}
        />
      )}
    </div>
  );
};

const ListAlert = ({ onClose }: { onClose?: () => void }) => {
  const [type, setType] = useState<string>("active");
  const [alerts, setAlerts] = useState<TAlert[]>([]);
  const [totalActive, setTotalActive] = useState<number>(0);
  const [totalExpired, setTotalExpired] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const TAB_ALERT = [
    {
      name: "Active",
      value: "active",
      total: totalActive,
    },
    {
      name: "Expired",
      value: "expired",
      total: totalExpired,
    },
  ];

  const getAlerts = async (isHasLoading = false) => {
    try {
      isHasLoading && setIsLoading(true);
      const res = await rf.getRequest("AlertRequest").getAllAlerts({
        type,
        limit: 100,
      });
      setAlerts(res.docs);
      setTotalExpired(res?.totalExpired);
      setTotalActive(res?.totalActive);
      isHasLoading && setIsLoading(false);
    } catch (e: any) {
      isHasLoading && setIsLoading(false);
      console.error(e);
    }
  };

  useEffect(() => {
    getAlerts(true).then();
  }, [type]);

  const onDeleteAlert = async () => {
    if (type === TAB_ALERT[0].value) {
      await onDeleteAlertActive();
    } else {
      await onDeleteAlertExpired();
    }
  };

  const onDeleteAlertActive = async () => {
    try {
      await rf.getRequest("AlertRequest").deleteAllAlertActive();
      toastSuccess("Successfully!", `Delete alert active successfully`);
      getAlerts().then();
    } catch (e: any) {
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const onDeleteAlertExpired = async () => {
    try {
      await rf.getRequest("AlertRequest").deleteAllAlertExpired();
      toastSuccess("Successfully!", `Delete alert expired successfully`);
      getAlerts().then();
    } catch (e: any) {
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  return (
    <div
      style={{
        background:
          "linear-gradient(0deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%), #06070e",
        backdropFilter: "blur(7.5px)",
      }}
      className="tablet:w-[450px] w-full rounded-[8px] pb-2"
    >
      <div className="tablet:pt-2 tablet:border-b-0 border-white-100 mb-2 flex cursor-pointer items-center justify-between border-b px-3">
        <div className="tablet:py-0 flex flex-1 items-center justify-between py-4">
          <div className="tablet:body-md-medium-14 heading-sm-medium-16 flex items-center gap-2">
            Alerts
            <ReloadIcon
              className="h-[12px] w-[12px] cursor-pointer"
              onClick={() => getAlerts(false)}
            />
          </div>
          <div
            className="tablet:hidden block"
            onClick={() => {
              onClose && onClose();
            }}
          >
            <CloseIcon />
          </div>
        </div>

        <div
          className="tablet:flex body-xs-medium-10 flex hidden items-center gap-1"
          onClick={onDeleteAlert}
        >
          <TrashIcon />
          Clear All
        </div>
      </div>

      <div className="border-white-50 mb-1 flex gap-2 border-b px-3">
        {TAB_ALERT.map((item, index) => {
          const isActive = item.value === type;
          return (
            <div
              onClick={() => setType(item.value)}
              key={index}
              className={`flex cursor-pointer gap-1 border-b p-1 ${
                isActive
                  ? "text-white-1000 border-white-500"
                  : "text-white-500 border-transparent"
              }`}
            >
              <div className="body-sm-medium-12 ">{item.name}</div>
              {item.total > 0 && (
                <div
                  className={`body-2xs-regular-8 flex h-4 w-4 items-center justify-center rounded-full border ${
                    isActive
                      ? "border-brand-800 bg-brand-800 text-brand-500"
                      : "border-white-50 bg-white-50"
                  }`}
                >
                  {item.total}
                </div>
              )}
            </div>
          );
        })}
      </div>
      <div className="tablet:h-[410px] customer-scroll h-[calc(100vh-152px)] overflow-y-auto">
        {isLoading ? (
          <div className="body-sm-regular-12 mt-4 text-center">Loading...</div>
        ) : (
          <>
            {alerts.length ? (
              alerts.map((item, index) => {
                return (
                  <AlertItem
                    key={index}
                    alert={item}
                    fetchData={getAlerts}
                    type={type}
                  />
                );
              })
            ) : (
              <div className="body-sm-regular-12 mt-4 text-center">
                No data...
              </div>
            )}
          </>
        )}
      </div>

      <div className="tablet:hidden border-white-100 block border-t p-4">
        <AppButton className="gap-2" onClick={onDeleteAlert}>
          <TrashIcon />
          Clear All
        </AppButton>
      </div>
    </div>
  );
};

export const Alert = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const isTablet = useMediaQuery({ maxWidth: 992 });

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.SHOW_LIST_ALERT, () => setIsShow(true));

    return () =>
      AppBroadcast.remove(BROADCAST_EVENTS.SHOW_LIST_ALERT, () =>
        setIsShow(true)
      );
  }, []);

  if (isTablet) {
    return (
      <>
        <div className="p-2" onClick={() => setIsShow(true)}>
          <TimerIcon />
        </div>

        {isShow && (
          <AppDrawer
            isOpen={isShow}
            toggleDrawer={() => setIsShow(false)}
            className={clsx("bottom-0 top-0 !w-full bg-[#141518]", "!h-full")}
            direction={"right"}
          >
            <ListAlert onClose={() => setIsShow(false)} />
          </AppDrawer>
        )}
      </>
    );
  }

  return (
    <AppPopover
      isOpen={isShow}
      onToggle={setIsShow}
      onClose={() => setIsShow(false)}
      trigger={
        <div
          className={`${
            isShow ? "bg-neutral-alpha-50" : ""
          } hover:bg-neutral-alpha-50 tablet:w-[30px] tablet:h-[30px] flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded-[4px]`}
        >
          <TimerIcon />
        </div>
      }
      content={<ListAlert />}
      position="left"
    />
  );
};
