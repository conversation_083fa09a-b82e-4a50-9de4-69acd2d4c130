"use client";

import BigNumber from "bignumber.js";
import clsx from "clsx";
import { jwtDecode } from "jwt-decode";
import * as React from "react";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { Virtuoso } from "react-virtuoso";
import { CloseIcon, CopyIcon, HistoryIcon, TwitterIcon } from "@/assets/icons";
import {
  AppButton,
  AppCopy,
  AppLogoNetwork,
  AppNumber,
  AppUserAddress,
} from "@/components";
import AppDrawer from "@/components/AppDrawer";
import config from "@/config";
import { useInitialing } from "@/hooks";
import { toastError, toastSuccess } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { setReferralMyCode } from "@/store/user.store";
import {
  T<PERSON><PERSON>mHistory,
  TClaimRequest,
  TReferralMetadata,
} from "@/types/referral.type";
import { formatNumber } from "@/utils/format";
import { copyToClipboard, filterParams } from "@/utils/helper";
import { MyReferral, TermsConditions } from "./parts";
import { isDefaultReferralCode } from "@/utils/referral";
import { NETWORKS } from "@/utils/contants";

const LINK_RETWEET = "https://x.com/RaidenxOfficial/status/1950503535910568061";

const DEFAULT_REFERRAL_LAYERS: TClaimRequest[] = [
  {
    level: 1,
    totalReferrals: 0,
    availableCommissions: "0",
    totalClaimed: "0",
    totalCommissions: "0",
    tradingVolume: "0",
    lifeTimeTradingVolume: "0",
    claimedTradingVolume: "0",
  },
  {
    level: 2,
    totalReferrals: 0,
    availableCommissions: "0",
    totalClaimed: "0",
    totalCommissions: "0",
    tradingVolume: "0",
    lifeTimeTradingVolume: "0",
    claimedTradingVolume: "0",
  },
  {
    level: 3,
    totalReferrals: 0,
    availableCommissions: "0",
    totalClaimed: "0",
    totalCommissions: "0",
    tradingVolume: "0",
    lifeTimeTradingVolume: "0",
    claimedTradingVolume: "0",
  },
  {
    level: 4,
    totalReferrals: 0,
    availableCommissions: "0",
    totalClaimed: "0",
    totalCommissions: "0",
    tradingVolume: "0",
    lifeTimeTradingVolume: "0",
    claimedTradingVolume: "0",
  },
  {
    level: 5,
    totalReferrals: 0,
    availableCommissions: "0",
    totalClaimed: "0",
    totalCommissions: "0",
    tradingVolume: "0",
    lifeTimeTradingVolume: "0",
    claimedTradingVolume: "0",
  },
];

const TABS = [
  {
    value: "my-referral",
    label: "My referral",
  },
  {
    value: "terms-and-conditions",
    label: "Terms & Conditions",
  },
];

const ClaimHistoryMobile = ({
  isOpen,
  claimRequests,
  loadMore,
  onCloseModal,
}: {
  isOpen: boolean;
  claimRequests: TClaimHistory[];
  loadMore: () => void;
  onCloseModal: () => void;
}) => {
  return (
    <AppDrawer
      isOpen={isOpen}
      toggleDrawer={onCloseModal}
      className={clsx("h-full w-full bg-[#141518]", "sm:w-[375px]")}
    >
      <div className="border-white-50 h-full w-full border bg-[#141518]">
        <div className="border-white-100 flex h-[50px] items-center justify-between border-b px-[16px] py-[12px]">
          <div className="heading-sm-medium-16">Claim History</div>
          <div className="cursor-pointer" onClick={onCloseModal}>
            <CloseIcon />
          </div>
        </div>
        <div className="h-[calc(100%-50px)] px-[16px] pt-[16px]">
          <Virtuoso
            className="customer-scroll"
            style={{ height: "100%" }}
            data={claimRequests}
            endReached={loadMore}
            increaseViewportBy={100}
            itemContent={(_, entry) => {
              return (
                <div
                  key={entry?.requestId}
                  className="rounded-4 border-white-50 bg-white-25 mb-[8px] border p-[10px]"
                >
                  <div className="mb-[4px] flex items-center justify-between">
                    <div className="text-white-500 text-[10px]">Request ID</div>
                    <div className="text-white-0 text-[10px]">
                      {entry.requestId}
                    </div>
                  </div>
                  <div className="mb-[4px] flex items-center justify-between">
                    <div className="text-white-500 text-[10px]">Wallet</div>
                    <div className="text-white-0 flex gap-1 text-[10px]">
                      <AppUserAddress
                        network={NETWORKS.SUI}
                        address={entry.receiver}
                        className="text-white-1000 !w-max"
                      />
                      <AppCopy
                        message={entry.receiver}
                        className="text-white-600 hover:text-white-1000 h-[12px] w-[12px]"
                      />
                    </div>
                  </div>
                  <div className="mb-[4px] flex items-center justify-between">
                    <div className="text-white-500 text-[10px]">Amount</div>
                    <div className="flex gap-2">
                      <div className="flex items-center gap-[4px] text-[12px] font-semibold">
                        {formatNumber(entry.totalAmount, 4, "0")}
                        <AppLogoNetwork
                          network={NETWORKS.SUI}
                          isBase
                          className="h-[12px] w-[12px]"
                        />
                      </div>
                      <div className="text-white-500 flex gap-1 text-[12px]">
                        <div className="text-white-500 flex items-center justify-center text-[16px] leading-[16px]">
                          {" "}
                          {" ≈ "}
                        </div>
                        <AppNumber
                          value={entry.totalAmount}
                          isForUSD
                          className="body-sm-regular-12"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-[4px] flex items-center justify-between">
                    <div className="text-white-500 text-[10px]">
                      Receive Status
                    </div>
                    <div className="text-white-0 flex gap-1 text-[10px]">
                      <div className="rounded-4 border border-green-900 bg-green-900 px-1 text-[10px] font-[500] capitalize leading-[16px] text-green-500">
                        {entry.status}
                      </div>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </div>
      </div>
    </AppDrawer>
  );
};

export default function ReferralPage() {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  // const { activeWalletAddresses } = useRaidenxWallet();
  const [referralMetadata, setReferralMetadata] =
    useState<TReferralMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [referralCode, setReferralCode] = useState<string>("");
  const [referralLink, setReferralLink] = useState<string>("");
  const [isShowClaimHistory, setIsShowClaimHistory] = useState<boolean>(false);
  const { isInitialing } = useInitialing();

  const [activeTab, setActiveTab] = useState<string>("my-referral");
  const [referralLayers, setReferralLayers] = useState<TClaimRequest[]>([]);
  const [claimRequests, setClaimRequests] = useState<TClaimHistory[]>([]);
  const [page, setPage] = useState(1);
  const [referralCodeError, setReferralCodeError] = useState("");
  const [isShowRetweetScreen, setIsShowRetweetScreen] = useState<boolean>(true);
  const LIMIT_PER_PAGE = 100;
  const dispatch = useDispatch();

  useEffect(() => {
    if (isInitialing) return;
  }, [isInitialing, accessToken]);

  useEffect(() => {
    if (!accessToken) return;
    Promise.all([
      getReferralMetadata(),
      getReferralLayers(),
      getClaimRequests(),
    ]);
    setPage(1);
  }, [accessToken]);

  const claimableAmount = useMemo(() => {
    return referralLayers.reduce(
      (acc, layer) => acc + Number(layer.availableCommissions),
      0
    );
  }, [referralLayers]);

  const decodedInfo = useMemo(() => {
    if (accessToken) {
      return jwtDecode(accessToken) as any;
    }

    return {};
  }, [accessToken]);

  useEffect(() => {
    if (!accessToken) return;
    if (
      referralMetadata &&
      !!referralMetadata?.myReferralCode &&
      !isDefaultReferralCode(
        referralMetadata?.myReferralCode,
        decodedInfo?.userId
      )
    ) {
      setIsShowRetweetScreen(false);
    }
  }, [accessToken, referralMetadata]);

  const getReferralMetadata = async () => {
    setIsLoading(true);
    try {
      const res = await rf.getRequest("ReferralRequest").getReferralMetadata();
      if (!!res) {
        setReferralMetadata(res);
        const code = isDefaultReferralCode(
          res.myReferralCode,
          decodedInfo?.userId
        )
          ? ""
          : res.myReferralCode;
        setReferralCode(code);
        setReferralLink(`${config.appUrl}@${code}`);
        if (!!res.myReferralCode) {
          dispatch(setReferralMyCode(res.myReferralCode));
        }
      }
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  };

  const registerReferralCode = async () => {
    if (!referralCode) {
      return toastError("Error", "Referral code is required!");
    }
    try {
      await rf.getRequest("ReferralRequest").registerReferralCode(referralCode);
      toastSuccess("Success", "Update Successfully!");
      await getReferralMetadata();
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const getReferralLayers = async () => {
    try {
      const res = await rf
        .getRequest("UserRequest")
        .getCommissions(NETWORKS.SUI);
      if (res && res.length > 0) {
        setReferralLayers(res);
      } else {
        setReferralLayers(DEFAULT_REFERRAL_LAYERS);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const createClaimRequest = async (walletAddress: string) => {
    if (!canClaim) {
      return;
    }
    try {
      const res = await rf
        .getRequest("UserRequest")
        .createClaimRequest(NETWORKS.SUI, { receiver: walletAddress });
      if (!!res) {
        toastSuccess("Success", "Get claim requests successfully!");
        getClaimRequests().then();
        getReferralLayers().then();
      }
    } catch (e) {
      toastError("Error", "Get claim requests failed!");
    }
  };

  const getClaimRequests = async () => {
    if (!accessToken) {
      return;
    }
    const paramsFilter = filterParams({
      page,
      limit: LIMIT_PER_PAGE,
    });
    try {
      const res = await rf
        .getRequest("UserRequest")
        .getClaimRequests(NETWORKS.SUI, paramsFilter);
      if (!!res) {
        setClaimRequests(res.docs);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const loadMore = () => {
    if (!claimRequests || claimRequests?.length / LIMIT_PER_PAGE + 1 === page)
      return;
    setPage((prevState) => prevState + 1);
  };

  const handleReferralCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.trim();
    if (/^[a-zA-Z0-9]*$/.test(newValue)) {
      setReferralCode(newValue);
      setReferralCodeError("");
    } else {
      setReferralCodeError(
        "Referral code must contain only letters and numbers."
      );
    }
  };

  const canClaim = useMemo(() => {
    return new BigNumber(claimableAmount).comparedTo(0) > 0;
  }, [claimableAmount]);

  const _renderTabContent = () => {
    switch (activeTab) {
      case "my-referral":
        return (
          <MyReferral
            referralLayers={referralLayers}
            claimRequests={claimRequests}
            loadMore={loadMore}
            createClaimRequest={createClaimRequest}
          />
        );
      case "terms-and-conditions":
        return <TermsConditions />;
      default:
        return (
          <MyReferral
            referralLayers={referralLayers}
            claimRequests={claimRequests}
            loadMore={loadMore}
            createClaimRequest={createClaimRequest}
          />
        );
    }
  };

  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const onRetweet = () => {
    setIsShowRetweetScreen(false);
  };

  const _renderContent = () => {
    if (!accessToken) {
      return (
        <div className="md:mt-[24px]">
          <TermsConditions />
        </div>
      );
    }

    if (isShowRetweetScreen) {
      return (
        <div className="flex flex-col items-center justify-center pt-5 md:pt-0">
          <div className="body-md-semibold-14 mb-1">
            Love what we offer? Share and Earn!
          </div>
          <div className="body-sm-regular-12 mb-4 text-center">
            Every time your friends use your link to start trading, you&apos;ll
            earn passive income based on their trading volume.
          </div>

          <div
            style={{
              boxShadow: "4px 4px 32px 0px rgba(0, 255, 204, 0.12)",
            }}
          >
            <img
              src="/images/TermConditionsReferral.png"
              alt="TermConditionsReferral"
            />
          </div>

          <a href={LINK_RETWEET} target="_blank">
            <AppButton
              className="my-4 mb-8 flex items-center gap-1"
              onClick={onRetweet}
            >
              <TwitterIcon className="h-4 w-4" /> Retweet to get started
            </AppButton>
          </a>
        </div>
      );
    }

    return (
      <>
        <div className="w-full md:w-2/3">
          <div className="mb-[16px] flex items-center justify-between">
            <div className="flex items-center">
              {TABS.map((tab) => {
                const isActive = tab?.value === activeTab;
                return (
                  <div
                    key={tab.value}
                    onClick={() => setActiveTab(tab?.value)}
                    className={clsx(
                      "min-h-[24px] cursor-pointer px-[4px] pb-[5px] pt-[3px] text-[12px] md:min-h-[40px] md:p-[10px] md:text-[14px]",
                      isActive
                        ? "text-white-1000 border-white-500 border-b font-semibold"
                        : "text-white-500"
                    )}
                  >
                    {tab?.label} {isActive}
                  </div>
                );
              })}
            </div>
            <HistoryIcon
              className="block md:hidden"
              onClick={() => setIsShowClaimHistory(true)}
            />
          </div>
          {_renderTabContent()}
          <ClaimHistoryMobile
            isOpen={isShowClaimHistory}
            claimRequests={claimRequests}
            loadMore={loadMore}
            onCloseModal={() => setIsShowClaimHistory(false)}
          />
        </div>
        <div className="w-full md:w-1/3">
          {accessToken && !isLoading && (
            <div className="bg-white-50 rounded-8 border-white-50 border p-[10px] md:p-[16px]">
              <div className="text-white-0 pb-[4px] font-semibold md:pb-[8px]">
                Your Referral link
              </div>

              {!!referralMetadata?.myReferralCode &&
              !isDefaultReferralCode(
                referralMetadata?.myReferralCode,
                decodedInfo?.userId
              ) ? (
                <div className="flex gap-[8px] md:block">
                  <div className="text-white-500 border-white-50 rounded-4 mb-0 w-[calc(100%-90px)] truncate border p-[8px] text-[14px] md:mb-[16px] md:w-full">
                    {referralLink}
                  </div>
                  <button
                    onClick={() => copyToClipboard(referralLink)}
                    className="text-white-0 hover:bg-white-300 rounded-6 bg-white-100 flex h-[32px] w-[90px] items-center gap-1 whitespace-nowrap p-[8px] text-[12px] font-medium"
                  >
                    <CopyIcon className="w-4" />
                    Copy Link
                  </button>
                </div>
              ) : (
                <>
                  <div className="flex gap-[8px] md:block">
                    <div className="flex items-center gap-[8px]">
                      <div className="text-white-500 text-[14px] max-md:text-sm">
                        {config.appUrl}@
                      </div>
                      <input
                        className="border-white-100 w-full rounded-[6px] border bg-transparent px-2 py-[6px] text-[14px] max-md:text-sm "
                        value={referralCode}
                        onChange={handleReferralCodeChange}
                      />
                    </div>
                    <AppButton
                      onClick={registerReferralCode}
                      className="mt-0 flex h-[32px] w-[80px] items-center justify-center whitespace-nowrap font-medium md:mt-4"
                    >
                      Create Link
                    </AppButton>
                  </div>

                  {referralCodeError && (
                    <p className="mt-1 text-sm text-red-500">
                      {referralCodeError}
                    </p>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </>
    );
  };

  return (
    <>
      {isMobile && isShowRetweetScreen && accessToken && (
        <div className="flex h-[118px] w-full flex-col items-center justify-center bg-[url('/images/BgReferralResponsive.png')] bg-cover bg-bottom bg-no-repeat">
          <div className="heading-md-semibold-18 text-white-900 mb-1">
            Referral Tracking
          </div>
          <div className="body-sm-medium-12 text-white-700">
            Share the value, reap the rewards with our Referral Program!
          </div>
        </div>
      )}

      {!isMobile && (
        <div className="flex h-[260px] w-full flex-col items-center justify-center bg-cover bg-bottom bg-no-repeat md:bg-[url('/images/BgReferral.png')]">
          <div className="heading-2xl-semibold-40 text-white-900 mb-2">
            Referral Tracking
          </div>
          <div className="body-md-regular-14 text-white-700">
            Share the value, reap the rewards with our Referral Program!
          </div>
        </div>
      )}

      <div className="mx-auto w-full max-w-[1200px] px-2 pt-[8px] md:px-4">
        <div className="flex w-full flex-col-reverse gap-[8px] md:flex-row md:gap-[32px]">
          {_renderContent()}
        </div>
      </div>
    </>
  );
}
