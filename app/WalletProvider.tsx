import { ReactNode } from "react";
import "@mysten/dapp-kit/dist/index.css";

import config from "@/config";
import { SuiClientProvider, WalletProvider } from "@mysten/dapp-kit";
import { getFullnodeUrl } from "@mysten/sui/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { PrivyProvider } from "@privy-io/react-auth";

const queryClient = new QueryClient();

const networks = {
  testnet: { url: config.rpcUrl },
  mainnet: { url: getFullnodeUrl("mainnet") },
  localnet: { url: getFullnodeUrl("localnet") },
  devnet: { url: getFullnodeUrl("devnet") },
};

const WalletExternalProvider = ({ children }: { children: ReactNode }) => {
  const defaultNetWork = config?.network || ("testnet" as any);

  return (
    <PrivyProvider
      appId={config?.privyConfig?.appId}
      config={{
        appearance: {
          theme: "dark",
        },
        loginMethods: ["google", "email", "twitter", "telegram"],
      }}
    >
      <QueryClientProvider client={queryClient}>
        <SuiClientProvider networks={networks} defaultNetwork={defaultNetWork}>
          <WalletProvider autoConnect>{children}</WalletProvider>
        </SuiClientProvider>
      </QueryClientProvider>
    </PrivyProvider>
  );
};

export default WalletExternalProvider;
