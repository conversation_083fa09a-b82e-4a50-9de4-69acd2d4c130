"use client";
import {
  Announcement,
  HomeIcon,
  LineChartIcon,
  Telegram,
  TwitterIcon,
  WebsiteIcon,
} from "@/assets/icons";
import { AppButton } from "@/components";
import AppInput from "@/components/AppInput";
import { PaymentWalletSelector } from "@/components/PaymentWalletSelector";
import { toastError, toastSuccess } from "@/libs/toast";
import { PreviewLogo } from "@/public/images";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { NETWORKS } from "@/utils/contants";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import AdvertisingFooter from "../Footer";
import CustomCheckbox from "../components/CustomCheckbox";

const advertisementPack = [
  { label: "3 H", value: 3, price: 40 },
  { label: "8 H", value: 8, price: 80 },
  { label: "24 H", value: 24, price: 100 },
];

const advertisementFactorMap: { [key: number]: number } = {
  0: 60 * 60 * 3,
  1: 60 * 60 * 8,
  2: 60 * 60 * 24,
};

export default function AdvertisingOrderPage() {
  const router = useRouter();
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [tokenAddress, setTokenAddress] = useState("");
  const [title, setTitle] = useState("");
  const [pitch, setPitch] = useState("");
  const [telegramUsername, setTelegramUsername] = useState("");
  const [agreeVerify, setAgreeVerify] = useState(false);
  const [agreePolicy, setAgreePolicy] = useState(false);
  const [isActiveTokenAdvertisement, setIsActiveTokenAdvertisement] =
    useState(false);

  const isPreviewFilled = !!title || !!pitch;

  const [selectedPack, setSelectedPack] = useState<number | null>(0);
  const [selectedWalletAddress, setSelectedWalletAddress] =
    useState<string>("");

  const selectedWallet = useMemo(
    () => wallets.find((item) => item.address === selectedWalletAddress),
    [wallets, selectedWalletAddress]
  );

  const selectedAdvertisementFactor = useMemo(
    () => advertisementFactorMap[selectedPack!],
    [selectedPack]
  );

  const isSufficientBalance = useMemo(() => {
    if (selectedPack === null || !selectedWallet) return false;
    const requiredAmount = advertisementPack[selectedPack].price;
    return Number(selectedWallet.balance) < requiredAmount;
  }, [selectedPack, selectedWallet]);

  const checkActiveTokenAdvertisement = useCallback(
    async (tokenAddress: string) => {
      try {
        const response = await rf
          .getRequest("TokenAdvertisementRequest")
          .checkActiveTokenAdvertisement(NETWORKS.SUI, tokenAddress);
        setIsActiveTokenAdvertisement(response.isActive);
      } catch (e: any) {
        toastError("Error", e?.message || "Something went wrong!");
        console.error(e);
      }
    },
    []
  );

  useEffect(() => {
    if (tokenAddress) {
      checkActiveTokenAdvertisement(tokenAddress);
    } else {
      setIsActiveTokenAdvertisement(false);
    }
  }, [tokenAddress, checkActiveTokenAdvertisement]);

  const onTransferTokenAdvertisement = async () => {
    try {
      const metadata = {
        tokenAddress,
        title,
        pitch,
        telegramUsername,
        advertisementFactor: selectedAdvertisementFactor,
      };

      await rf.getRequest("WithdrawRequest").transfer(NETWORKS.SUI, {
        amountIn: "",
        fromWallet: selectedWalletAddress,
        metadata,
        paymentCategory: "TOKEN_ADVERTISEMENT",
        toWallet: "",
        withdrawalType: "payment",
      });

      toastSuccess("Success", "The transfer has been completed successfully!");
      router.push("/advertising/order/done");
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.error(e);
    }
  };

  return (
    <div className="flex min-h-screen w-full flex-col items-center  bg-[url('/images/BgAdvertising.png')] bg-auto bg-fixed bg-top bg-no-repeat px-2 sm:px-4 md:px-0">
      <div className="body-md-regular-14 mx-auto flex flex-wrap items-center justify-center gap-2 pb-2 pt-6 md:pt-[56px]">
        <HomeIcon />
        <span
          className="text-white-500 flex cursor-pointer items-center gap-2 no-underline"
          onClick={() => router.push("/")}
        >
          Home <span>/</span>
        </span>
        <span
          className="text-white-500 cursor-pointer no-underline"
          onClick={() => router.push("/advertising")}
        >
          Token Advertising
        </span>
        <span>/</span>
        <span className="text-white-1000">Order</span>
      </div>

      <div className="text-custom-gradient mb-2 text-[32px] font-medium leading-[120%] sm:text-[40px] md:text-[48px]">
        Token Advertising
      </div>

      <form
        onSubmit={onTransferTokenAdvertisement}
        className="mt-10 w-full max-w-[400px]"
      >
        <div className="action-sm-medium-12 text-white-700 mb-4">
          <AppInput
            label="Token Address"
            value={tokenAddress}
            onChange={(e) => setTokenAddress(e.target.value)}
            placeholder="Enter token address here"
            maxLength={200}
            className="body-sm-medium-12 text-white-1000 placeholder:text-white-300"
          />
          {isActiveTokenAdvertisement && (
            <div className="body-xs-regular-10 mb-4 text-red-500">
              This token is already requested for advertising.
            </div>
          )}
        </div>

        <div className="relative mb-4">
          <AppInput
            label="Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter title here"
            maxLength={50}
            className="body-sm-regular-12 text-white-1000 placeholder:text-white-300 pr-16"
          />
          <span className="text-white-1000 placeholder:text-white-300 body-md-regular-14 absolute right-4 top-[34px]">
            {title.length}/50
          </span>
        </div>
        <div className="relative mb-4">
          <div className="action-sm-medium-12 text-white-700 mb-2">Pitch</div>
          <AppInput
            label={
              <span className="text-white-500">
                A short description of your project to get people interested
              </span>
            }
            value={pitch}
            onChange={(e) => setPitch(e.target.value)}
            placeholder="Enter title here"
            maxLength={120}
            className="body-sm-regular-12 text-white-1000 placeholder:text-white-300 pr-20"
          />
          <span className="text-white-1000 body-md-regular-14 absolute right-4 top-[57px]">
            {pitch.length}/120
          </span>
        </div>
        <div className="mb-4">
          <label className="text-white-1000 heading-sm-medium-16 mb-2 block">
            Preview
          </label>
          {!isPreviewFilled ? (
            <div className="text-white-500 body-xs-regular-10 mb-4">
              Please fill out a form to see an ad preview!
            </div>
          ) : (
            <>
              <div className="text-white-500 body-xs-regular-10 mb-2">
                Your token logo and social links will be fetched from RaidenX
                profile. If it&apos;s not up to date,
                <a href="#" className="text-brand-500 mx-1 underline">
                  click here
                </a>
                to sync from your token profile.
              </div>
              <div className="mb-4 flex w-full justify-center">
                <div className="bg-white-50 relative flex w-full max-w-[292px] flex-col gap-2 rounded-xl p-4 shadow-lg">
                  <div className="flex items-center gap-3">
                    <Image
                      src={PreviewLogo}
                      alt="token"
                      width={41}
                      height={41}
                      className="rounded-full"
                    />
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center">
                        <span className="text-white-1000 min-w-0 flex-1 truncate text-[13.7px] font-semibold leading-[20.54px]">
                          {title || "Token Name"}
                        </span>
                        <span className="bg-white-100 text-white-900 ml-2 flex-shrink-0 rounded px-1 text-[8.56px] font-medium leading-[13.7px]">
                          Ad
                        </span>
                      </div>
                      <div className="text-white-500 mt-1 whitespace-pre-line break-words text-[11.98px] font-normal leading-[17.12px]">
                        {pitch || "Pitch preview..."}
                      </div>
                    </div>
                  </div>
                  <div className="my-[8px] flex items-center gap-2">
                    <div className="bg-white-100 action-sm-medium-12 text-white-1000 flex h-[32px] w-[158px] items-center justify-center gap-1 rounded-[6px]">
                      <LineChartIcon />
                      <div className="action-sm-medium-12">Chart</div>
                    </div>

                    <div className="bg-white-100 flex h-8 w-8 items-center justify-center rounded-[6px] p-2">
                      <WebsiteIcon />
                    </div>
                    <div className="bg-white-100 flex h-8 w-8 items-center justify-center rounded-[6px] p-2">
                      <TwitterIcon />
                    </div>
                    <div className="bg-white-100 flex h-8 w-8 items-center justify-center rounded-[6px] p-2">
                      <Telegram />
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Announcement />
                      <div className="text-white-500 text-[11.98px] font-normal leading-[11.98px]">
                        Advertise your token
                      </div>
                    </div>
                    <div className="text-white-500 text-[11.98px] font-normal leading-[11.98px]">
                      Hide ad
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        <div className="mb-4">
          <div className="text-white-1000 body-md-regular-14 mb-[12px] block">
            Contact info <span className="text-white-500">(Optional)</span>
          </div>
          <div className="text-white-500 body-xs-regular-10 mb-4">
            Provide your Discord and or Telegram usernames if you want us to be
            able to quickly reach you in case of any additional questions or
            clarifications. This can speed up the process of approving your
            order!
          </div>
        </div>
        <div className="action-sm-medium-12 text-white-700 mb-4">
          <AppInput
            label="Telegram Username"
            value={telegramUsername}
            onChange={(e) => setTelegramUsername(e.target.value)}
            placeholder="Enter here"
            className="body-sm-regular-12 placeholder:text-white-300 text-white-1000"
          />
        </div>
        <div className="mb-4 flex flex-col gap-4">
          <CustomCheckbox
            checked={agreeVerify}
            onChange={setAgreeVerify}
            label="I understand that all supplied data must be verifiable through official channels such as website and socials."
          />

          <CustomCheckbox
            checked={agreePolicy}
            onChange={setAgreePolicy}
            label="I understand and accept that RaidenX reserves the right to reject or modify the provided information."
          />

          <div className="text-white-1000 body-md-regular-14">
            By completing this purchase, I confirm that I&apos;ve read and agree
            to the <span className="underline">Refund Policy</span>.
          </div>
        </div>

        <div className="mb-4 mt-4">
          <label className="text-white-1000 mb-2 block">Duration</label>
          <div className="grid grid-cols-3 gap-4">
            {advertisementPack.map((pack, index) => (
              <div
                key={index}
                className={`relative flex cursor-pointer select-none flex-col items-center rounded-lg border p-4 transition-all ${
                  selectedPack === pack.value
                    ? "bg-white-50 border-brand-500"
                    : "border-white-100"
                }`}
                onClick={() => setSelectedPack(index)}
              >
                {selectedPack === index && (
                  <div className="">
                    <span className="body-xs-medium-10 text-brand-500 absolute right-2 top-2 flex h-[18px] w-[18px] items-center justify-center rounded bg-[#27D9711A] px-1">
                      ✓
                    </span>
                  </div>
                )}
                <span className="heading-md-semibold-18 text-brand-500 mb-4">
                  {pack.label}
                </span>
                <span className="text-white-500 body-md-regular-14">
                  {pack.price} SUI
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <label className="text-white-1000 mb-1 block">Choose wallet</label>
          <PaymentWalletSelector
            setSelectedWalletAddress={setSelectedWalletAddress}
            isOnlySelect
          />
          {isSufficientBalance && (
            <div className="mt-2 text-[12px] text-red-500">
              Insufficient balance. Please select a different wallet.
            </div>
          )}
        </div>
        <div className="mb-8 mt-8 flex flex-col gap-4 sm:gap-4 md:mb-[95px]">
          <AppButton
            variant="buy"
            size="large"
            className="w-full"
            disabled={
              !title ||
              !tokenAddress ||
              !agreeVerify ||
              !agreePolicy ||
              !selectedWalletAddress ||
              isSufficientBalance
            }
            onClick={onTransferTokenAdvertisement}
          >
            Order Now
          </AppButton>
        </div>
      </form>
      <AdvertisingFooter />
    </div>
  );
}
