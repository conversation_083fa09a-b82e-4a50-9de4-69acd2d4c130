import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export const usePairBoost = () => {
  const [isShowBoostModal, setIsShowBoostModal] = useState(false);
  const searchParams = useSearchParams();

  useEffect(() => {
    if (!searchParams?.get("isBoost")) return;
    setIsShowBoostModal(true);
  }, [searchParams?.get("isBoost")]);

  return { isShowBoostModal, setIsShowBoostModal };
};
