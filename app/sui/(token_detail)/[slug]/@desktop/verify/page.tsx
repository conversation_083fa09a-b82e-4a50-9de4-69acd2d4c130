"use client";

import React, { useContext, useMemo, useState } from "react";
import { BackVerifyIcon } from "@/assets/icons";
import { TPair } from "@/types";
import { useParams, useRouter } from "next/navigation";
import VerifyTokenProfile from "@/components/PoolVerify/parts/verify-token-profile.part";
import CommunityTakeover from "@/components/PoolVerify/parts/community-takeover.part";
import MyTickets from "@/components/PoolVerify/parts/my-tickets";
import { RootPairContext } from "../../../provider";

const PoolVerifyContent = () => {
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const params = useParams<{
    pairSlug: string;
  }>();
  const router = useRouter();

  console.log("params", params);

  const handleBack = () => {
    if (pair) {
      router.push(`/sui/${params?.pairSlug}`);
    }
  };
  const [activeTab, setActiveTab] = useState("list-verify");

  const TAB = [
    {
      id: "verify-token-profile",
      name: "Verify Token Profile",
    },
    {
      id: "community-takeover",
      name: "Community Takeover",
    },
    {
      id: "list-verify",
      name: "My Tickets",
    },
  ];
  const renderTab = useMemo(() => {
    switch (activeTab) {
      case "verify-token-profile":
        return (
          <div>
            {pair?.tokenBase?.bannerImageUrl ? (
              <div className="text-white-1000 mt-10 text-center text-[18px] font-semibold">
                Token already verified. You can only complete the community
                takeover.
              </div>
            ) : (
              <VerifyTokenProfile changeTab={setActiveTab} />
            )}
          </div>
        );
      case "community-takeover":
        return <CommunityTakeover changeTab={setActiveTab} />;
      case "list-verify":
        return <MyTickets />;
      default:
        return <VerifyTokenProfile changeTab={setActiveTab} />;
    }
  }, [activeTab]);

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex w-full max-w-[1187px] flex-col">
        <div className="flex items-center gap-4 p-8">
          <div
            className="bg-white-100 rounded-6 hover:bg-white-200 flex h-[32px] w-[32px] cursor-pointer items-center justify-center p-2"
            onClick={handleBack}
          >
            <BackVerifyIcon />
          </div>
          <div className="text-[18px] font-semibold leading-[21.6px]">
            Verify Token
          </div>
        </div>
        <div className="flex">
          {TAB.map((item: any, index) => {
            return (
              <div
                onClick={() => setActiveTab(item.id)}
                key={index}
                className={`hover:text-neutral-alpha-1000 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] leading-[16px] hover:font-semibold ${
                  activeTab === item.id
                    ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b font-semibold"
                    : "text-neutral-alpha-800 border-0 font-normal"
                }`}
              >
                {item.name}{" "}
              </div>
            );
          })}
        </div>
        <div className="flex-1">{renderTab}</div>
      </div>
    </div>
  );
};

export default function PoolVerifyPage() {
  return (
    <>
      <PoolVerifyContent />
    </>
  );
}
