"use client";

import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { useContext, useEffect, useMemo, useState } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { TPair } from "@/types";
import { TradingView } from "@/components/TradingView";
import { PairTableList } from "@/components/Pair/PairTableList";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";

export const SectionCenter = ({ isHomePage }: { isHomePage?: boolean }) => {
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const [sectionExpanded, setSectionExpanded] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const onLayoutChange = (layout: any) => {
    AppBroadcast.dispatch(BROADCAST_EVENTS.LAYOUT_CHANGED, { layout });
  };

  const toggleSection = (component: string) => {
    setSectionExpanded((prevSection) => {
      if (!prevSection) return component;
      if (prevSection === component) return null;
      if (prevSection === "CHART" && component === "TABLE") return null;
      return component;
    });
  };

  const expand = (component: string) => toggleSection(component);

  const hide = (component: string) => {
    if (!sectionExpanded) {
      setSectionExpanded(component === "TABLE" ? "CHART" : null);
    } else if (
      sectionExpanded === component ||
      (sectionExpanded === "CHART" && component === "CHART")
    ) {
      setSectionExpanded(null);
    }
  };

  const bottomPanelHeight = useMemo(() => {
    if (!isClient) return 0;
    if (isHomePage) {
      return Math.floor((300 / 688) * 100);
    }
    return Math.floor((300 / (window.innerHeight - 52)) * 100);
  }, [isClient, isHomePage]);

  if (!isClient) {
    return null;
  }

  if (!sectionExpanded) {
    return (
      <PanelGroup direction="vertical" onLayout={onLayoutChange}>
        <Panel
          id="top-panel"
          defaultSize={100 - bottomPanelHeight}
          minSize={15}
        >
          {pair?.pairId && <TradingView pair={pair} />}
        </Panel>
        <PanelResizeHandle
          id="resize-handle"
          className="bg-neutral-alpha-150 relative h-[1.5px]"
        >
          <div className="absolute left-1/2 top-1/2 z-10 flex -translate-x-1/2 -translate-y-1/2 flex-col gap-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.0008 1.37488C10.1707 1.37488 10.3333 1.44404 10.4511 1.56645L12.6329 3.83317C12.8723 4.08186 12.8648 4.47752 12.6161 4.7169C12.3674 4.95627 11.9717 4.94872 11.7324 4.70003L10.6258 3.55045V7.59545C10.6258 7.94062 10.346 8.22045 10.0008 8.22045C9.65565 8.22045 9.37583 7.94062 9.37583 7.59545V3.55045L8.26931 4.70003C8.02993 4.94872 7.63427 4.95627 7.38558 4.7169C7.13689 4.47752 7.12934 4.08187 7.36872 3.83317L9.55053 1.56645C9.66835 1.44404 9.83093 1.37488 10.0008 1.37488ZM1.375 9.9997C1.375 9.65452 1.65482 9.3747 2 9.3747L18 9.3747C18.3452 9.3747 18.625 9.65453 18.625 9.9997C18.625 10.3449 18.3452 10.6247 18 10.6247L2 10.6247C1.65482 10.6247 1.375 10.3449 1.375 9.9997ZM10.0008 11.8751C10.346 11.8751 10.6258 12.1549 10.6258 12.5001V16.5451L11.7323 15.3955C11.9717 15.1468 12.3674 15.1393 12.616 15.3786C12.8647 15.618 12.8723 16.0137 12.6329 16.2624L10.4511 18.5291C10.3333 18.6515 10.1707 18.7207 10.0008 18.7207C9.8309 18.7207 9.66833 18.6515 9.5505 18.5291L7.36869 16.2624C7.12931 16.0137 7.13686 15.618 7.38555 15.3786C7.63424 15.1393 8.0299 15.1468 8.26928 15.3955L9.3758 16.5451V12.5001C9.3758 12.1549 9.65562 11.8751 10.0008 11.8751Z"
                fill="#2B2C32"
              />
            </svg>
          </div>
        </PanelResizeHandle>
        <Panel id="bottom-panel" defaultSize={bottomPanelHeight} minSize={10}>
          <PairTableList
            isHomePage={isHomePage}
            sectionExpanded={sectionExpanded}
            expand={expand}
            hide={hide}
            pair={pair}
          />
        </Panel>
      </PanelGroup>
    );
  }

  if (sectionExpanded === "TABLE") {
    return (
      <PairTableList
        isHomePage={isHomePage}
        sectionExpanded={sectionExpanded}
        expand={expand}
        hide={hide}
        pair={pair}
      />
    );
  }

  if (sectionExpanded === "CHART") {
    return (
      <>
        {/*isHomePage height is 688px*/}
        {/*40px header of pool insight*/}
        <div className={isHomePage ? "h-[648px]" : "h-[calc(100vh-40px)]"}>
          {pair?.pairId && <TradingView pair={pair} />}
        </div>

        <div>
          <PairTableList
            isHomePage={isHomePage}
            sectionExpanded={sectionExpanded}
            isHidden={true}
            expand={expand}
            hide={hide}
            pair={pair}
          />
        </div>
      </>
    );
  }
  return null;
};
