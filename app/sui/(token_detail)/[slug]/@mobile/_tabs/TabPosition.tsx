import React, { useMemo, useState } from "react";
import { TableMyPosition } from "@/components/Table/TableMyPosition";
import { TPair } from "@/types";
import { isIOSMobile } from "@/utils/helper";
import useWindowSize from "@/hooks/useWindowSize";
import { useMediaQuery } from "react-responsive";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

type Props = {
  pair: TPair;
};

export const TabPosition = ({ pair }: Props) => {
  const [isHideOtherPair, setIsHideOtherPair] = useState(false);
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );

  const handleHideOtherPair = () => {
    setIsHideOtherPair(!isHideOtherPair);
  };

  const { windowHeight } = useWindowSize();
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const tableHeight = useMemo(() => {
    if (isMobile) {
      if (isIOSMobile()) {
        if (!isHideInstallApp) {
          return windowHeight - 150 - 8 - 40;
        }
        return windowHeight - 150 - 8;
      }

      if (!isHideInstallApp) {
        return windowHeight - 150 - 40;
      }
      return windowHeight - 150;
    }
    return windowHeight - 133;
  }, [windowHeight]);

  return (
    <div className="px-[8px]">
      <div className="body-md-medium-14 text-white-1000 flex items-center justify-between gap-2 px-[8px] py-[8px]">
        <div>Positions</div>
        <div
          className="action-xs-medium-12 text-brand-500 cursor-pointer leading-[16px]"
          onClick={handleHideOtherPair}
        >
          {isHideOtherPair ? "Show Other Tokens" : "Hide Other Tokens"}
        </div>
      </div>
      <div>
        <TableMyPosition
          heightContent={tableHeight}
          pair={pair}
          isHideOtherPair={isHideOtherPair}
        />
      </div>
    </div>
  );
};
