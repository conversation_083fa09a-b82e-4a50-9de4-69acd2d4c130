import React, { useMemo } from "react";
import { TrashSmallIcon } from "@/assets/icons";
import { useSelector } from "react-redux";
import { AppButton } from "@/components";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair, TPairPrice } from "@/types";
import { isIOSMobile } from "@/utils/helper";
import useWindowSize from "@/hooks/useWindowSize";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { PairOpenOrders } from "@/components/Pair/PairOpenOrder";
import { NETWORKS } from "@/utils/contants";
import { usePairPrice } from "@/hooks/usePairPrice";

export const TabOrder = ({ pair }: { pair: TPair }) => {
  const { pairPrice } = usePairPrice(pair);
  const { windowHeight } = useWindowSize();
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );

  // const [isOpenModalCancelAll, setIsOpenModalCancelAll] =
  //   useState<boolean>(false);

  const cancelAllOrders = async () => {
    try {
      await rf.getRequest("NewOrderRequest").cancelAllOrder(NETWORKS.SUI);
      toastSuccess("Success", "Cancel successfully!");
      AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const tableHeight = useMemo(() => {
    if (isIOSMobile()) {
      if (!isHideInstallApp) {
        return windowHeight - 150 - 8 - 40;
      }
      return windowHeight - 150 - 8;
    }

    if (!isHideInstallApp) {
      return windowHeight - 150 - 40;
    }
    return windowHeight - 150;
  }, [windowHeight]);

  return (
    <div className="px-[8px]">
      <div className="body-md-medium-14 text-white-1000 mb-1 flex h-[40px] items-center justify-between gap-2 px-[8px] py-[8px]">
        <div>Open Orders</div>
        <AppButton
          variant="outline"
          className="body-xs-medium-10 gap-1 py-[5px]"
          onClick={cancelAllOrders}
        >
          <TrashSmallIcon />
          <span className="text-[10px] leading-[14px]">Cancel All</span>
        </AppButton>
      </div>
      <div>
        <PairOpenOrders heightContent={tableHeight} pair={pair} />
      </div>
    </div>
  );
};
