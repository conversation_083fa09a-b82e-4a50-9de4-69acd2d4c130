import { PairOrderHistories } from "@/components/Pair/PairOrderHistories";
import useWindowSize from "@/hooks/useWindowSize";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { isIOSMobile } from "@/utils/helper";
import { useMemo, useState } from "react";
import { useSelector } from "react-redux";

export const TabOrderHistory = ({ pair }: { pair: TPair }) => {
  const { windowHeight } = useWindowSize();
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const [isHideOtherPair, setIsHideOtherPair] = useState(false);

  const handleHideOtherPair = () => {
    setIsHideOtherPair(!isHideOtherPair);
  };

  const tableHeight = useMemo(() => {
    if (isIOSMobile()) {
      if (!isHideInstallApp) {
        return windowHeight - 150 - 8 - 40;
      }
      return windowHeight - 150 - 8;
    }

    if (!isHideInstallApp) {
      return windowHeight - 150 - 40;
    }
    return windowHeight - 150;
  }, [windowHeight]);

  return (
    <div className="px-[8px]">
      <div className="body-md-medium-14 text-white-1000 mb-1 flex h-[40px] items-center justify-between gap-2 px-[8px] py-[8px]">
        <div>Order History</div>
        <div
          className="action-xs-medium-12 text-brand-500 cursor-pointer leading-[16px]"
          onClick={handleHideOtherPair}
        >
          {isHideOtherPair ? "Show Other Tokens" : "Hide Other Tokens"}
        </div>
      </div>
      <div>
        <PairOrderHistories
          heightContent={tableHeight}
          pair={pair}
          isHideOtherPair={isHideOtherPair}
        />
      </div>
    </div>
  );
};
