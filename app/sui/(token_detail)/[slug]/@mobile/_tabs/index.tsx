"use client";

import clsx from "clsx";
import React, { useContext, useState } from "react";
import { useSelector } from "react-redux";
import {
  ArrowSwitch,
  IMyTradeIcon,
  InforIcon,
  IOpenOrderIcon,
  IPositionIcon,
} from "@/assets/icons";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { isIOSMobile } from "@/utils/helper";
import useWindowSize from "@/hooks/useWindowSize";

import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { PairPoolStats } from "@/components/Pair/PairPoolStats";

import { TabTrade } from "./TabTrade";
import { TabInfor } from "./TabInfor";
import { TabActivity } from "./TabActivity";
import { TabPosition } from "./TabPosition";
import { TabOrder } from "./TabOrder";
import { TabOrderHistory } from "./TabOrderHistory";

enum ETabMobile {
  TRADE = "trade",
  INFOR = "infor",
  ACTIVITIES = "activities",
  POSITIONS = "positions",
  ORDERS = "orders",
  ORDER_HISTORIES = "order-histories",
}

export default function MobileNavigationTabs() {
  const { windowHeight } = useWindowSize();
  const [activeTabMobile, setActiveTabMobile] = useState<string>(
    ETabMobile.TRADE
  );

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };

  const TAB_MOBILE = [
    {
      id: ETabMobile.TRADE,
      name: "Trade",
      icon: ArrowSwitch,
    },
    {
      id: ETabMobile.INFOR,
      name: "Infor",
      icon: InforIcon,
    },
    {
      id: ETabMobile.ACTIVITIES,
      name: "Txns",
      icon: IMyTradeIcon,
    },
    {
      id: ETabMobile.ORDERS,
      name: "Orders",
      icon: IOpenOrderIcon,
      hideWhenWithoutLogin: true,
    },
    {
      id: ETabMobile.ORDER_HISTORIES,
      name: "History",
      icon: IOpenOrderIcon,
      hideWhenWithoutLogin: true,
    },
    {
      id: ETabMobile.POSITIONS,
      name: "Positions",
      icon: IPositionIcon,
      hideWhenWithoutLogin: true,
    },
  ].filter(Boolean);

  const renderTabs = () => {
    switch (activeTabMobile) {
      case ETabMobile.TRADE:
        return <TabTrade pair={pair} />;
      case ETabMobile.INFOR:
        return <TabInfor pair={pair} />;
      case ETabMobile.ACTIVITIES:
        return <TabActivity />;
      case ETabMobile.POSITIONS:
        return <TabPosition pair={pair} />;
      case ETabMobile.ORDERS:
        return <TabOrder pair={pair} />;
      case ETabMobile.ORDER_HISTORIES:
        return <TabOrderHistory pair={pair} />;
      default:
        return "Trade";
    }
  };

  return (
    <div
      style={{
        height: `${isHideInstallApp ? windowHeight : windowHeight - 40}px`,
      }}
    >
      <div
        className={`tablet:hidden flex h-[calc(100%-58px)] flex-col overflow-auto`}
      >
        <PairPoolStats pair={pair} />
        <div className={`h-[calc(100%-50px)]`}>{renderTabs()}</div>
      </div>

      <div
        className={`grid ${
          !!accessToken ? "grid-cols-6" : "grid-cols-3"
        } tablet:hidden border-white-50 bg-black-700 bottom-0 left-0 right-0 z-50 flex w-full justify-center border-t`}
      >
        {TAB_MOBILE.map((item: any) => {
          const isActive = activeTabMobile === item.id;
          if (item.hideWhenWithoutLogin && !accessToken) return null;
          return (
            <div
              key={item?.id}
              style={{
                backdropFilter: "blur(calc(16px / 2))",
              }}
              className={clsx(
                "border-white-50 flex h-[56px] cursor-pointer flex-col items-center justify-center gap-1 px-1 py-[6px]",
                isIOSMobile() ? "pb-[18px] pt-[6px]" : "py-[6px]",
                isActive
                  ? "text-neutral-alpha-1000 border-t-white-500 border-t-[1px] bg-[#1f2027]"
                  : "text-white-500 border-r bg-[#08080c]"
              )}
              onClick={() => setActiveTabMobile(item.id)}
            >
              <div>
                <item.icon
                  className={`${
                    isActive
                      ? "custom-opacity-svg-active text-white"
                      : "custom-opacity-svg-inactive"
                  }`}
                />
              </div>
              <div className="body-xs-medium-10 text-center leading-[14px]">
                {item?.name}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
