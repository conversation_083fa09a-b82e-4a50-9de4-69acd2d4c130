"use client";

import * as React from "react";
import { T<PERSON><PERSON>, TPair<PERSON>rice } from "@/types";
import {
  AppButton,
  AppCopy,
  AppN<PERSON>ber,
  BaseToken,
  TradingView,
} from "@/components";
import { useContext } from "react";
import { isDexHasBondingCurve } from "@/utils/dex";
import { formatToPercent } from "@/utils/format";
import {
  FlashIcon,
  CloseAuditCheckIcon,
  DetailChartIcon,
} from "@/assets/icons";
import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { ModalQuickTrade } from "@/modals/ModalQuickTrade";
import { useState } from "react";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { RootPairContext } from "../../sui/(token_detail)/provider";
import { NETWORKS } from "@/utils/contants";
import { useLogin } from "@/hooks";

export const PairChartDetails = ({
  isPosition,
  onRemoveChart,
}: {
  isPosition?: boolean;
  onRemoveChart?: (pairSlug: string) => void;
}) => {
  const [isShowModalQuickTrade, setIsShowModalQuickTrade] =
    useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);

  const { pairPrice, pair } = useContext(RootPairContext) as {
    pairPrice: TPairPrice;
    pair: TPair;
  };

  const dispatch = useDispatch();
  const { onLogin } = useLogin();

  if (!pair?.pairId) return <></>;

  const onQuickBuy = () => {
    if (!accessToken) {
      onLogin();
      return;
    }

    if (!wallets.length) {
      dispatch(setIsShowModalAddWallet({ isShow: true }));
      return;
    }

    setIsShowModalQuickTrade(true);
  };

  return (
    <div className="flex h-full w-full flex-col justify-end">
      <div className="bg-black-800 flex h-max justify-between p-2">
        <div className="flex items-center gap-2">
          <div>
            <BaseToken pair={pair} />
          </div>

          <div>
            <div className="flex items-center gap-2">
              <div>{pair?.tokenBase?.symbol}</div>
              <AppCopy message={pair?.tokenBase?.address} />
            </div>
            <div className="flex items-center gap-2">
              <AppNumber
                value={pairPrice?.priceUsd}
                isForUSD
                className="body-sm-medium-12 text-white-800"
              />
              <div className="body-xs-regular-10 text-white-500 flex items-center gap-1">
                MC
                <div className="body-sm-medium-12 text-white-800">
                  <AppNumber value={pair?.marketCapUsd} isForUSD />
                </div>
              </div>
              <div className="body-xs-regular-10 text-white-500 flex items-center gap-1">
                Liq
                <div className="body-sm-medium-12 text-white-800">
                  <AppNumber value={pair?.liquidityUsd} isForUSD />
                </div>
              </div>
              {isDexHasBondingCurve(pair?.dex?.dex) && (
                <div className="body-xs-regular-10 text-white-500 flex items-center gap-1">
                  BC
                  <div className="body-sm-medium-12 text-white-800">
                    {formatToPercent(pair?.bondingCurve).toString()}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1">
          <AppButton
            variant="buy"
            size="small"
            className="gap-1"
            onClick={onQuickBuy}
          >
            <FlashIcon className="h-4 w-4" /> Quick Buy
          </AppButton>

          <Link href={`/${NETWORKS.SUI}/${pair?.slug}`}>
            <AppButton
              variant="secondary"
              size="small"
              className="h-[26px] w-[26px]"
            >
              <DetailChartIcon className="h-4 w-4" />
            </AppButton>
          </Link>

          {!isPosition && (
            <AppButton
              onClick={() => onRemoveChart && onRemoveChart(pair?.slug)}
              variant="secondary"
              size="small"
              className="h-[26px] w-[26px]"
            >
              <CloseAuditCheckIcon className="h-4 w-4" />
            </AppButton>
          )}
        </div>
      </div>
      <div className="h-max flex-1">
        <TradingView pair={pair} />
      </div>

      {isShowModalQuickTrade && (
        <ModalQuickTrade
          pair={pair}
          isOpen={isShowModalQuickTrade}
          onClose={() => setIsShowModalQuickTrade(false)}
        />
      )}
    </div>
  );
};
