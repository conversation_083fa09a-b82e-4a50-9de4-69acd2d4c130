"use client";

import { jwtDecode } from "jwt-decode";
import React, { useEffect, useState, Suspense } from "react";
import { useSelector } from "react-redux";
import { usePathname, useSearchParams } from "next/navigation";
import { RaidenIcon, Telegram } from "@/assets/icons";
import config from "@/config";
import Storage from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { isValidUrl } from "@/utils/helper";

const InvalidRedirectUri = ({ imageUrl }: { imageUrl: string }) => {
  return (
    <div>
      <div className="border-neutral-alpha-100 flex h-[64px] w-full items-center justify-center border-b py-2">
        <img
          src="/images/LogoRaidenXWhiteBlue.png"
          alt=""
          className="h-[40px]"
        />
      </div>
      <div className="flex h-full w-full items-center justify-center pt-[100px]">
        <div id="webcrumbs">
          <div className="border-neutral-alpha-100 w-[480px] rounded-2xl border bg-gray-900 p-6 font-sans text-white shadow-lg max-md:w-[360px]">
            <div className="flex flex-col items-center space-y-4">
              {imageUrl ? (
                <img src={imageUrl} alt="" className="h-[40px]" />
              ) : (
                <RaidenIcon />
              )}

              <h1 className="text-center text-xl font-bold">
                Access blocked: This app&apos;s request is invalid
              </h1>

              <div className="w-full space-y-4">
                You can&apos;t sign in because this app send an invalid request.
                You can try again later, or contact the developer about this
                issue.
              </div>

              <div className="mt-4 text-xs text-gray-400">
                Error 400: redirect_uri is invalid
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ContentAuthorize = ({ scope }: { scope: string }) => {
  const [permissions, setPermissions] = useState<any>(null);

  useEffect(() => {
    (async () => {
      const permissions = await rf
        .getRequest("AuthorizeRequest")
        .getPermissions();
      console.log(permissions, "permissions");
      setPermissions(permissions);
    })();
  }, []);

  const getListPermissions = (scope: string) => {
    const scopes = scope.split(",");
    return (
      permissions?.filter((permission: any) =>
        scopes.includes(permission.key)
      ) || []
    );
  };

  return (
    <div className="w-full space-y-4">
      <div>
        <h2 className="mb-2 font-bold">Things this App can do:</h2>
        <ul className="space-y-2 text-sm text-gray-400">
          {getListPermissions(scope)?.map((permission: any) => (
            <li key={permission.id}>• {permission.name}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

const AuthorizeContent = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  // const scopeExample = [
  //   'full_read_only',
  //   'order.market.write',
  //   'order.limit.write',
  // ];
  const [isInvalidRedirectUri, setIsInvalidRedirectUri] = useState(false);
  const [loading, setLoading] = useState(true);
  const [clientInfo, setClientInfo] = useState<any>(null);

  const getLoginUrl = () => {
    Storage.setRedirectAfterLogin(`${pathname}${searchParams.toString()}`);
    const referralCode = Storage.getReferralCode();
    if (referralCode) {
      return `${config.link_telegram}?start=${referralCode}`;
    }
    return `${config.link_telegram}?start=login`;
  };

  const client_id = searchParams.get("client_id");
  const redirect_uri = searchParams.get("redirect_uri");
  const scope = searchParams.get("scope");

  const validateScope = (scope: string) => {
    const scopeArray = scope.split(",");
    const validScope = scopeArray.every((item) =>
      [
        "order.full_access",
        "withdraw.full_access",
        "settings.full_access",
      ].includes(item)
    );
    return validScope;
  };

  useEffect(() => {
    if (!client_id) {
      return;
    }

    const init = async () => {
      try {
        const clientInfo = await rf
          .getRequest("AuthorizeRequest")
          .getPublicInfoClient({
            clientId: client_id || "",
          });
        setClientInfo(clientInfo);
      } catch (error) {
        console.log(error);
      }
    };

    init();
  }, [client_id]);

  useEffect(() => {
    (async () => {
      if (client_id) {
        try {
          setLoading(true);
          const checkRedirectUriRes = await rf
            .getRequest("AuthorizeRequest")

            .checkRedirectUri({
              clientId: client_id || "",
              redirectUri: redirect_uri || "",
            });
          if (checkRedirectUriRes) {
            setIsInvalidRedirectUri(false);
          }
        } catch (error) {
          setIsInvalidRedirectUri(true);
        } finally {
          setLoading(false);
        }
      }
    })();
  }, [client_id]);

  if (!redirect_uri || !scope || !client_id || !validateScope(scope)) {
    return <InvalidRedirectUri imageUrl={clientInfo?.logoUri} />;
  }

  // useEffect(() => {
  //   (async () => {
  //     if (accessToken) {
  //       const res = await rf.getRequest('AuthorizeRequest').createUserClient({
  //         logoUri:
  //           'https://storage.googleapis.com/raidenx-prod/logo/sui/0xe9a376ada59b2ad927eaedc086dd0d7649b56433598ceda4f9d86ab95309e651::puff::PUFF.png',
  //         name: 'RaidenX',
  //         redirectUris: ['https://agentfai.vercel.app/'],
  //       });
  //       console.log('res', res);
  //     }
  //   })();
  // }, [accessToken]);
  // 5340fc02-7be5-485f-90d0-32cdbd87733e => http://localhost:3000
  // 642b4d1e-95bf-4417-8e1d-4f98aa73fdd7 => https://agentfai.vercel.app/
  // a54169ee-b7b4-4b2a-a0ca-f797abece402 => https://raidenx.io/trending

  const createHiddenInput = (name: string, value: string) => {
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = name;
    input.value = value;
    return input;
  };

  const handleCancel = async () => {
    const form = document.createElement("form");
    form.method = "POST";
    form.action = `${config.authorizeApiUrl}/authorize/consent?action=deny`;
    form.appendChild(createHiddenInput("authorize", accessToken));
    form.appendChild(createHiddenInput("clientId", client_id || ""));
    form.appendChild(createHiddenInput("scopes", scope));
    form.appendChild(createHiddenInput("accessDuration", "86400"));
    form.appendChild(createHiddenInput("redirectUri", redirect_uri));
    document.body.appendChild(form);
    form.submit();
  };

  const handleAuthorizeApp = async () => {
    if (!isValidUrl(redirect_uri)) {
      setIsInvalidRedirectUri(true);
      return;
    }
    const form = document.createElement("form");
    form.method = "POST";
    form.action = `${config.authorizeApiUrl}/authorize/consent?action=accept`;
    form.appendChild(createHiddenInput("authorize", accessToken));
    form.appendChild(createHiddenInput("clientId", client_id || ""));
    form.appendChild(createHiddenInput("scopes", scope));
    form.appendChild(createHiddenInput("accessDuration", "86400"));
    form.appendChild(createHiddenInput("redirectUri", redirect_uri));
    document.body.appendChild(form);
    form.submit();
  };

  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center pt-[100px]">
        Loading...
      </div>
    );
  }
  if (isInvalidRedirectUri) {
    return <InvalidRedirectUri imageUrl={clientInfo?.logoUri} />;
  }

  const renderPolicyAndTerms = () => {
    return (
      <>
        <div className="mt-4 text-xs text-gray-400">
          Made by{" "}
          <a href="#" className="text-blue-400 hover:underline">
            {clientInfo?.name}
          </a>
          . Read {clientInfo?.name}&apos;s
          <a
            href={clientInfo?.privacyPolicy || "javascript:void(0)"}
            className="ml-1 text-blue-400 hover:underline"
          >
            privacy policy
          </a>{" "}
          and
          <a
            href={clientInfo?.terms || "javascript:void(0)"}
            className="ml-1 text-blue-400 hover:underline"
          >
            terms
          </a>
        </div>
        <div className="!pt-10 text-xs text-gray-400">
          Learn more about 3rd party app access in the{" "}
          <a
            href="https://docs.raidenx.io/docs/dca-order"
            target="_blank"
            className="text-blue-400 hover:underline"
          >
            help center
          </a>
          .
        </div>
      </>
    );
  };

  if (!accessToken) {
    return (
      <div>
        <div className="border-neutral-alpha-100 flex h-[64px] w-full items-center justify-center border-b py-2">
          {/* <RaidenIcon /> */}
          <img
            src={"/images/LogoRaidenXWhiteBlue.png"}
            alt=""
            className="h-[40px]"
          />
        </div>
        <div className="flex h-full w-full items-center justify-center pt-[100px]">
          <div id="webcrumbs">
            <div className="border-neutral-alpha-100 w-[480px] rounded-2xl border bg-gray-900 p-6 font-sans text-white shadow-lg max-md:w-[360px]">
              <div className="flex flex-col items-center space-y-4">
                {clientInfo?.logoUri && (
                  <img src={clientInfo?.logoUri} alt="" className="h-[40px]" />
                )}
                <h1 className="text-center text-xl font-bold">
                  Sign In to RaidenX
                </h1>
                <a
                  href={getLoginUrl()}
                  className="flex w-full items-center justify-center"
                >
                  <div className="bg-white-900 text-black-900 flex w-full items-center justify-center space-x-2 rounded-full py-3 text-center font-bold transition-colors hover:bg-gray-200">
                    <Telegram className="h-[16px] w-[16px]" />
                    <span>Sign In</span>
                  </div>
                </a>
                {clientInfo?.description && (
                  <p className="mt-4 text-sm text-gray-400">
                    {clientInfo?.description}
                  </p>
                )}

                {renderPolicyAndTerms()}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const decodedInfo = jwtDecode(accessToken) as any;

  return (
    <div>
      <div className="border-neutral-alpha-100 flex h-[64px] w-full items-center justify-center border-b py-2">
        {/* <RaidenIcon /> */}
        <img
          src={"/images/LogoRaidenXWhiteBlue.png"}
          alt=""
          className="h-[40px]"
        />
      </div>
      <div className="flex h-full w-full items-center justify-center pt-[100px]">
        <div id="webcrumbs">
          <div className="border-neutral-alpha-100 w-[480px] rounded-2xl border bg-gray-900 p-6 font-sans text-white shadow-lg max-md:w-[360px]">
            <div className="flex flex-col items-center space-y-4">
              <img src={clientInfo?.logoUri} alt="" className="h-[40px]" />

              <h1 className="text-center text-xl font-bold">
                {clientInfo?.name} wants to access your RaidenX account.
              </h1>

              <div className="border-neutral-alpha-50 flex w-full items-center space-x-3 rounded-lg border bg-gray-800 p-3">
                <div className="bg-white-50 flex h-10 w-10 items-center justify-center rounded-full font-bold text-white">
                  {decodedInfo?.displayName?.charAt(0)}
                </div>
                <div className="flex flex-col">
                  <span className="font-semibold">
                    {decodedInfo?.displayName}
                  </span>
                  <span className="text-sm text-gray-400">
                    @{decodedInfo?.userName}
                  </span>
                </div>
              </div>

              <button
                className="bg-white-900 text-black-900 w-full rounded-full py-3 font-bold transition-colors hover:bg-gray-200"
                onClick={handleAuthorizeApp}
              >
                Authorize app
              </button>

              <button
                className="font-medium text-red-400 transition-colors hover:text-red-300"
                onClick={handleCancel}
              >
                Cancel
              </button>

              {clientInfo?.description && (
                <p className="mt-4 text-sm text-gray-400">
                  {clientInfo?.description}
                </p>
              )}

              <ContentAuthorize scope={scope} />

              {renderPolicyAndTerms()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AuthorizePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthorizeContent />
    </Suspense>
  );
}
