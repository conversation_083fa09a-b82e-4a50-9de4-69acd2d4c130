"use client";

import LandingPage from "@/app/(landing)/page";
import Storage from "@/libs/storage";

interface Props {
  params: Promise<{
    username: string;
  }>;
}

export default async function HomePage({ params }: Props) {
  const paramsPage = await params;
  const { username } = paramsPage;
  const decodedUsername = decodeURIComponent(username);
  if (decodedUsername?.startsWith("@")) {
    const referralCode = decodedUsername.replace("@", "");
    console.log("referralCode", referralCode);
    if (!Storage.getReferralCode()) {
      Storage.setReferralCode(referralCode);
    }
    window.location.href = "/";
  }
  return <LandingPage />;
}
