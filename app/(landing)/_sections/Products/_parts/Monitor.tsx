"use client";

import React, { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { MonitorImage } from "@/public/images";
import { RootPairProvider } from "@/app/sui/(token_detail)/provider";
import { TPair } from "@/types";
import { NETWORKS } from "@/utils/contants";
import rf from "@/services/RequestFactory";
import { OrderForm } from "@/components";
import { PairAuditCheck } from "@/components/Pair/AuditCheck";
import CurveProcessing from "@/components/Pair/CurveProcessing";
import { SectionCenter } from "@/app/sui/(token_detail)/[slug]/@desktop/_sections/SectionCenter";
const PAIR_DEFAULT = "cetus-shr0-sui-236113";

export const Monitor = () => {
  const isMobile = useMediaQuery({ query: "(max-width: 1200px)" });
  const [selectedPair, setSelectedPair] = useState<TPair>({} as TPair);

  useEffect(() => {
    rf.getRequest("PairRequest")
      .getPair(NETWORKS.SUI, PAIR_DEFAULT)
      .then((res) => {
        setSelectedPair(res);
      });
  }, []);

  if (isMobile) {
    return (
      <div>
        <img className="mx-auto mt-4" src={MonitorImage.src} alt="app-mobile" />
      </div>
    );
  }
  if (!selectedPair?.slug) return null;
  return (
    <div className="border-white-150 h-[688px] overflow-hidden rounded-[16px] border">
      <RootPairProvider externalPair={selectedPair}>
        <div className="flex h-full">
          <div className="h-full max-w-[676px] flex-1">
            <SectionCenter isHomePage />
          </div>
          <div className="bg-neutral-alpha-50 flex h-[688px] w-[324px] flex-col">
            <OrderForm pair={selectedPair} />
            <CurveProcessing />
            <div className="customer-scroll flex-1 overflow-auto">
              <div>
                <PairAuditCheck />
              </div>
            </div>
          </div>
        </div>
      </RootPairProvider>
    </div>
  );
};
