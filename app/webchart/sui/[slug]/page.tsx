import * as React from "react";
import { TPair } from "@/types";
import { TradingView } from "@/components/TradingView";
import { normalizeStructTag } from "@mysten/sui/utils";
import { RootPairProvider } from "../../../sui/(token_detail)/provider";
import config from "@/config";
import { headers } from "next/headers";
import MessageListener from "./components/MessageListener";

async function getTokenDetail(slug: string) {
  const decodedSlug = decodeURIComponent(slug);
  let res;
  if (
    decodedSlug &&
    decodedSlug.startsWith("0x") &&
    normalizeStructTag(decodedSlug)
  ) {
    res = await fetch(`${config.apiUrl}/sui/tokens/${decodedSlug}/top-pair`, {
      next: { revalidate: 5 }, // Clear cache after 5 seconds
    });
  } else {
    res = await fetch(`${config.apiUrl}/sui/pairs/${decodedSlug}`, {
      next: { revalidate: 5 }, // Clear cache after 5 seconds
    });
  }
  const pair: TPair = await res.json();
  return pair;
}
interface LayoutProps {
  params: Promise<{ slug: string }>;
}

export default async function WebChartPage({ params }: LayoutProps) {
  const headersList = await headers();
  const { slug } = await params;
  const pair = await getTokenDetail(slug);
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");

  return (
    <RootPairProvider externalPair={pair} enableGraduatedPairChecker={false}>
      <div className="h-screen">
        <TradingView pair={pair} device={isMobile ? "mobile" : "desktop"} />
      </div>
      <MessageListener />
    </RootPairProvider>
  );
}
