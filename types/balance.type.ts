import { CoinBalance } from "@mysten/sui/client";
import { TWallet } from "./wallet.type";

export type TBalance = {
  userId?: string;
  walletAddress: string;
  walletType?: string;
  token: {
    id?: number | null;
    address: string;
    name?: string;
    symbol?: string;
    logoImageUrl?: string;
    iconUrl?: string;
  };
  listPairId?: string[];
  balance: string;
  balanceUsd?: string;
  balanceToUsd?: string;
  totalHolder?: number;
};

export type TBalanceOnchain = CoinBalance & { owner: string };

export type TTradingWallet = TWallet & {
  suiBalance: string;
  baseBalance: string;
  quoteBalance: string;
  baseBalance2Usd?: string;
};
