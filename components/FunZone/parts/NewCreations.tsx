import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { Virtuoso } from "react-virtuoso";
import { BoxQuickBuy } from "@/components/ListPair";
import useWindowSize from "@/hooks/useWindowSize";
import Storage from "@/libs/storage";
import { handleUpdateFunZoneSocials } from "@/utils/funzone";
import { isIOSMobile } from "@/utils/helper";
import { MemeItem } from "../MemeItem";
import { PAIR_FILTER_STORAGE_KEY } from "@/constants";
import { DEXES_DEFAULT } from "../ButtonFillter";
import { MEME_DEXES } from "@/enums/dex.enum";

const DEXES_ACTIVE = ["movepump", "turbosfun", "sevenkfun"];
const BONDING_CURVE_THRESH_HOLD = 0.2;

export const convertParamsMeme = (params: any) => {
  if (!params?.dexes) return { ...params, dexes: DEXES_DEFAULT };
  const arrDex = params?.dexes?.split(",");
  const dexesMeme = arrDex.filter((item: any) => MEME_DEXES.includes(item));
  return { ...params, dexes: dexesMeme.join(",") };
};

export const NewCreations = ({
  buyAmount,
  renderTabMobile,
  setBuyAmount,
  params,
  isHomePage,
}: {
  buyAmount: string;
  renderTabMobile?: any;
  isHomePage?: boolean;
  params?: any;
  setBuyAmount: (value: any) => void;
}) => {
  const [memes, setMemes] = useState<TPair[]>([]);
  const { network } = useSelector((state: RootState) => state.user);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const [newItemIndex, setNewItemIndex] = useState<number | null>(null);
  const memeRef = useRef<any>(memes);
  const { windowHeight } = useWindowSize();
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );

  const getMeme = async () => {
    try {
      const data = await rf
        .getRequest("MemeRequest")
        .getMemeNewCreated(network, convertParamsMeme(params));
      setMemes(data);
      memeRef.current = data;
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    Storage.setPairSearch(PAIR_FILTER_STORAGE_KEY, params);
    getMeme().then();
  }, [params]);

  const handleWhenItemUpdate = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    const newMemes = memeRef?.current as TPair[];

    // Find the meme being updated
    const updatedMemeIndex = newMemes.findIndex(
      (item) => item.pairId === data.pairId
    );

    if (updatedMemeIndex === -1) return;

    newMemes[updatedMemeIndex] = {
      ...newMemes[updatedMemeIndex],
      volumeUsd: data.volumeUsd,
      reserveQuote: data.reserveQuote,
      tokenBase: {
        ...newMemes[updatedMemeIndex].tokenBase,
        priceUsd: data.priceUsd,
      },
    };

    setMemes(newMemes);
    return;
  };

  const handleWhenBondingCurveUpdated = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    const newMemes = memeRef?.current as TPair[];
    const updatedMemeIndex = newMemes.findIndex(
      (item) => item.pairId === data.pairId
    );
    if (updatedMemeIndex === -1) return;
    newMemes[updatedMemeIndex] = {
      ...newMemes[updatedMemeIndex],
      bondingCurve: data?.bondingCurve,
    };
    setMemes(newMemes);

    const selectedMeme = newMemes[updatedMemeIndex];

    if (+selectedMeme?.bondingCurve >= +BONDING_CURVE_THRESH_HOLD) {
      const updatedMemes = newMemes.filter(
        (meme) => meme.pairId !== data.pairId
      );
      setMemes(updatedMemes);
      AppBroadcast.dispatch(BROADCAST_EVENTS.PAIR_MIGRATING, {
        ...selectedMeme,
        reserveQuote: data?.reserveQuote,
      });
    }
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.PAIR_STATS_UPDATED, handleWhenItemUpdate);
    AppBroadcast.on(
      BROADCAST_EVENTS.BONDING_CURVE_UPDATED,
      handleWhenBondingCurveUpdated
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenItemUpdate
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.BONDING_CURVE_UPDATED,
        handleWhenBondingCurveUpdated
      );
    };
  }, []);

  const handleWhenNewPairCreated = async (event: TBroadcastEvent) => {
    const data: any = event.detail;

    const newPair = await rf
      .getRequest("PairRequest")
      .getPair(data.network, data.pairId);

    newPair.timestamp = new Date().getTime() / 1000;

    if (
      !DEXES_ACTIVE.includes(newPair?.dex?.dex) ||
      (params?.dexes && !params?.dexes?.includes(newPair?.dex?.dex))
    ) {
      return;
    }

    memeRef.current = [newPair, ...memeRef.current];

    setMemes(memeRef.current);
    setNewItemIndex(0);
    setTimeout(() => {
      setNewItemIndex(null);
    }, 1000);
  };

  const handleWhenSocialsUpdate = async (event: TBroadcastEvent) => {
    if (!memeRef.current) return;
    const newMemes = handleUpdateFunZoneSocials(memeRef.current, event);
    memeRef.current = newMemes;
    setMemes(newMemes);
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.PAIR_CREATED, handleWhenNewPairCreated);
    AppBroadcast.on(
      BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
      handleWhenSocialsUpdate
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
        handleWhenSocialsUpdate
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_CREATED,
        handleWhenNewPairCreated
      );
    };
  }, [params]);

  const heightVirtuoso = useMemo(() => {
    if (isHomePage) {
      return 454;
    }

    if (isMobile) {
      if (isIOSMobile()) {
        if (!isHideInstallApp) {
          return windowHeight - 220 - 8 - 40;
        }
        return windowHeight - 220 - 8;
      }

      if (!isHideInstallApp) {
        return windowHeight - 220 - 40;
      }

      return windowHeight - 220;
    }

    return windowHeight - 203;
  }, [windowHeight, isMobile, isHideInstallApp]);

  return (
    <div className="border-white-100 border border-x-0 border-b-0">
      <div className="border-white-100 flex items-center justify-between border-b p-[8px] pl-[8px] md:pl-[20px]">
        {renderTabMobile ? (
          renderTabMobile()
        ) : (
          <div className="body-md-semibold-14">New Creations</div>
        )}

        <div className="flex items-center gap-1">
          {isMobile && (
            <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
          )}
        </div>
      </div>

      <div className="flex flex-col px-[8px] py-[12px]">
        <Virtuoso
          style={{ height: heightVirtuoso }}
          className="customer-scroll"
          data={memes}
          itemContent={(index, item) => {
            const isNewItem = newItemIndex === index;
            return (
              <div
                key={index}
                className={`${isNewItem ? "animate-new-transaction" : ""}`}
              >
                <MemeItem
                  key={`${item.dex?.name}-${item.pairId}-${index}`}
                  buyAmount={buyAmount}
                  meme={item}
                  filterParams={params}
                />
              </div>
            );
          }}
        />
      </div>
    </div>
  );
};
