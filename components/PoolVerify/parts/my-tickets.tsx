import React, { useContext, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  LimitedOfferIcon,
  PreviewVerifyIcon,
  StandardIcon,
} from "@/assets/icons";
import { AppButton, AppLogoNetwork } from "@/components";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";

import { PreviewToken } from "./preview-token.part";
import { TPair } from "@/types";
import { SubmitHandler, useForm } from "react-hook-form";
import { toastError, toastSuccess } from "@/libs/toast";
import config from "@/config";
import {
  IFormTranfer,
  ListHistoryRes,
  PayType,
  StatusHistory,
  VerifyType,
} from "../types";
import ApplicationItem from "../ApplicationItem";
import { SelectedWalletPayment } from "../SelectedWalletPayment";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
// import UploadSection from '../components/UploadSection';

interface InputFieldProps {
  label: string;
  placeholder?: string;
  prefix?: React.ReactNode;
  value: string;
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  placeholder,
  prefix,
  value,
}) => {
  return (
    <div className="flex flex-col gap-2">
      <span className="text-white-700 text-[12px] leading-[16px]">{label}</span>
      <div className="border-white-100 bg-white-50 flex items-center rounded-[6px] border">
        {prefix && (
          <span className="text-white-500 bg-white-100 flex h-full items-center rounded-l-[6px] p-2 text-[12px]">
            {prefix}
          </span>
        )}
        <input
          disabled={true}
          value={value}
          placeholder={placeholder}
          className="placeholder:text-white-300 bg-white-50 body-sm-medium-12 flex w-full items-center p-[8px] outline-none disabled:opacity-50"
        />
      </div>
    </div>
  );
};

const MyTickets = () => {
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const network = "sui";
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [activeTab, setActiveTab] = useState("verify-token-profile");
  const [history, setHistory] = useState<ListHistoryRes[]>([]);
  const [historySelected, setHistorySelected] = useState<ListHistoryRes>();
  const [walletSelected, setWalletSelected] = useState<string>("");
  // const [deletePreview, setDeletePreview] = useState<any>(null);
  // const [isUploading, setIsUploading] = useState(false);
  // const [logoImage, setLogoImage] = useState<File | null>(null);
  // const [bannerImage, setBannerImage] = useState<File | null>(null);

  const [isPaying, setIsPaying] = useState(false);

  const getHistories = async () => {
    try {
      const data = await rf.getRequest("ExternalRequest").getPoolVerifyHistory({
        tokenAddress: pair?.tokenBase?.address,
      });
      setHistory(data?.docs as ListHistoryRes[]);
      if (data?.docs?.length) {
        setHistorySelected(data?.docs[0] || null);
        setActiveTab(
          data?.docs[0]?.type === VerifyType.ORIGINAL
            ? "verify-token-profile"
            : "community-takeover"
        );
      }
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    if (pair?.tokenBase?.address) {
      getHistories().then();
    }
  }, [pair?.tokenBase?.address]);

  useEffect(() => {
    if (wallets.length) {
      setWalletSelected(wallets[0]?.address || "");
    }
  }, [wallets]);

  const handleClickHistory = (item: ListHistoryRes) => {
    setHistorySelected(item);
  };

  const {
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<IFormTranfer>({
    criteriaMode: "all",
  });

  const watchAll: IFormTranfer = watch();

  const onSubmit: SubmitHandler<IFormTranfer> = async (data: IFormTranfer) => {
    try {
      setIsPaying(true);
      if (data) {
        const payload = {
          amountIn:
            historySelected?.paymentInfo.type === PayType.LIMITED_OFFER
              ? config.verifyToken.limited.price.toString()
              : config.verifyToken.standard.price.toString(),
          fromWallet: walletSelected,
          metadata: { tokenProfileId: historySelected?.id },
          paymentCategory: "CREATE_TOKEN_PROFILE",
          toWallet: "",
          withdrawalType: "payment",
        };

        await rf.getRequest("WithdrawRequest").transfer(network, payload);

        // setHistory((prevHistory) =>
        //   prevHistory.map((item) =>
        //     item.id === historySelected?.id
        //       ? { ...item, status: StatusHistory.PAID }
        //       : item,
        //   ),
        // );

        // setHistorySelected((prev) =>
        //   prev ? { ...prev, status: StatusHistory.PAID } : prev,
        // );
      }
      toastSuccess(
        "Payment successful!",
        "You can view your application in My tickets"
      );

      setTimeout(() => {
        getHistories().then(() => setIsPaying(false));
      }, 5000);
    } catch (e: any) {
      toastError(
        "Payment failed!",
        e.message ||
          "Your payment was not successful. Please try again or contact support for further assistance"
      );
      console.error(e);
    } finally {
    }
  };

  const infoWalletSelected = wallets.find(
    (item) => item.address === walletSelected
  );

  const isSufficientBalance = useMemo(() => {
    if (historySelected?.paymentInfo.type === PayType.LIMITED_OFFER) {
      return (
        Number(infoWalletSelected?.balance) <
        Number(config.verifyToken.limited.price)
      );
    }
    return (
      Number(infoWalletSelected?.balance) <
      Number(config.verifyToken.standard.price)
    );
  }, [infoWalletSelected, historySelected?.paymentInfo.type]);

  const disableLimitedOffer = useMemo(() => {
    const allFieldsFilled = Object.keys(watchAll).every(
      (key) => key === "website" || !!watchAll[key as keyof IFormTranfer]
    );

    const noErrors = Object.keys(errors).length === 0;
    const isSelected =
      historySelected?.paymentInfo.type === PayType.LIMITED_OFFER;

    return !(allFieldsFilled && noErrors && isSelected && !isSufficientBalance);
  }, [errors, watchAll, walletSelected, infoWalletSelected]);

  const disableStandardPricing = useMemo(() => {
    const allFieldsFilled = Object.keys(watchAll).every(
      (key) =>
        key === "website" ||
        key === "alreadyInstalled" ||
        key === "alreadyBacklink" ||
        !!watchAll[key as keyof IFormTranfer]
    );

    const isErrors = Object.keys(errors).some(
      (key) => key !== "alreadyInstalled" && key !== "alreadyBacklink"
    );
    const isSelected =
      historySelected?.paymentInfo.type === PayType.STANDARD_PRICING;

    return !(
      allFieldsFilled &&
      !isErrors &&
      isSelected &&
      !isSufficientBalance
    );
  }, [errors, watchAll, walletSelected, infoWalletSelected]);

  const TAB = [
    {
      id: "verify-token-profile",
      name: "Verify Token Profile",
    },
    {
      id: "community-takeover",
      name: "Community Takeover",
    },
  ];

  const renderTab = useMemo(() => {
    const filteredHistory = history?.filter((item: ListHistoryRes) =>
      activeTab === "verify-token-profile"
        ? item.type === VerifyType.ORIGINAL
        : item.type !== VerifyType.ORIGINAL
    );

    return (
      <div className="customer-scroll  max-h-[360px] max-w-[380px] overflow-y-auto">
        {filteredHistory?.map((item: ListHistoryRes, index: number) => (
          <div key={index}>
            <ApplicationItem
              isSelected={item.id === historySelected?.id}
              type={item.status}
              isHistory
              data={item}
              onClick={() => handleClickHistory(item)}
            />
          </div>
        ))}
      </div>
    );
  }, [activeTab, history, historySelected]);

  return (
    <>
      {historySelected ? (
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="flex gap-2">
            <div className="flex flex-col gap-3 p-4">
              <ApplicationItem
                type={historySelected?.status || StatusHistory.SUBMITTED}
              />
              <div className="text-white-1000 text-[12px] font-semibold">
                1. Token Info
              </div>
              <div className="flex flex-col gap-2">
                <InputField
                  label="Token Address"
                  placeholder="Enter your Token Address"
                  value={historySelected?.tokenAddress || ""}
                />
              </div>

              <div className="flex flex-col gap-2">
                <InputField
                  label="Contact information"
                  placeholder="Enter your Telegram ID"
                  prefix="https://t.me/"
                  value={historySelected?.contact || ""}
                />
              </div>
              <div className="flex gap-3">
                <div className="flex w-1/2 flex-col gap-2">
                  <InputField
                    label="Twitter"
                    prefix="https://twitter.com/"
                    value={
                      historySelected?.twitter?.split("https://x.com/")[1] || ""
                    }
                  />
                </div>
                <div className="flex w-1/2 flex-col gap-2">
                  <InputField
                    label="Telegram"
                    prefix="https://t.me/"
                    value={
                      historySelected?.telegram?.split("https://t.me/")[1] || ""
                    }
                  />
                </div>
              </div>
              <div className="mb-3 flex flex-col gap-2">
                <InputField
                  label="Website"
                  placeholder="Enter the new official websites link"
                  value={historySelected?.website || ""}
                />
              </div>
              {/* <AgreementCheckbox
                name="agreeDataProvided"
                label="I agree that all the data provided above can be verified through other channels, and the information I submit will be publicly displayed."
              />

              <AgreementCheckbox
                name="agreeReview"
                label="I agree to RaidenX's review methods and standards, and accept that RaidenX reserves the right to reject or modify the data."
              />

              <AgreementCheckbox
                name="agreePayment"
                label="By completing the payment, I confirm that I've read and agree that there is no refund in all circumstances."
              /> */}

              {historySelected?.status === StatusHistory.SUBMITTED && (
                <>
                  <div className="text-white-1000 mt-2 text-[12px] font-semibold">
                    2. Payment
                  </div>
                  <div>
                    <div className="text-white-700 mb-2 text-[12px] leading-[16px]">
                      Choose wallet
                    </div>

                    <SelectedWalletPayment
                      walletAddressSelected={walletSelected || ""}
                      wallets={wallets}
                      onSelect={setWalletSelected}
                      isOnlySelect
                    />
                    {isSufficientBalance && (
                      <div className="mt-2 text-[12px] text-red-500">
                        Insufficient balance. Please select a different wallet.
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-3 ">
                    {historySelected?.paymentInfo.type ===
                      PayType.LIMITED_OFFER && (
                      <div
                        className={`rounded-8 flex h-[120px] w-full cursor-pointer flex-col justify-between gap-4 p-3 ${
                          historySelected.paymentInfo.type ===
                          PayType.LIMITED_OFFER
                            ? "border-white-700 border "
                            : ""
                        }`}
                        style={{
                          background:
                            "linear-gradient(180deg, rgba(246, 139, 30, 0.10) 0%, rgba(144, 81, 18, 0.10) 100%)",
                        }}
                      >
                        <div className="rounded-6 flex h-[28px] w-[115px] cursor-pointer items-center justify-center gap-1 !bg-[rgba(246,139,30,0.10)] text-[12px] font-semibold text-orange-500 hover:!bg-orange-700">
                          <LimitedOfferIcon />
                          Limited Offer
                        </div>

                        {/* <div className="flex flex-col">
                        <AgreementCheckbox
                          name="alreadyInstalled"
                          className="mb-1 !w-full !items-start"
                          label={
                            <div>
                              I have installed{' '}
                              <span>
                                <a
                                  href="https://t.me/RaidenX_Buy_Bot"
                                  target="_blank"
                                  className="underline"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  https://t.me/RaidenX_Buy_Bot
                                </a>
                              </span>{' '}
                              to the project's telegram group
                            </div>
                          }
                          register={register}
                          validation={{
                            required:
                              selectOptionsPay === PayType.LIMITED_OFFER
                                ? 'You must agree to continue'
                                : false,
                          }}
                        />
                        <AgreementCheckbox
                          name="alreadyBacklink"
                          className="mb-1 !w-full !items-start"
                          label={
                            <div>
                              I have included RaidenX logo and backlink into the
                              project's website with regard to RaidenX{' '}
                              <span>
                                <a
                                  href="https://t.me/RaidenX_Buy_Bot"
                                  target="_blank"
                                  className="underline"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  media guideline
                                </a>
                              </span>{' '}
                            </div>
                          }
                          register={register}
                          validation={{
                            required:
                              selectOptionsPay === PayType.LIMITED_OFFER
                                ? 'You must agree to continue'
                                : false,
                          }}
                        />
                      </div> */}

                        <div className="flex items-center gap-3">
                          <button
                            type="submit"
                            disabled={
                              disableLimitedOffer ||
                              isPaying ||
                              isSufficientBalance
                            }
                          >
                            <AppButton
                              className="h-[40px] w-[60%] min-w-[225px] text-[14px] font-[500]"
                              disabled={
                                disableLimitedOffer ||
                                isPaying ||
                                isSufficientBalance
                              }
                            >
                              {isPaying ? "Paying..." : "Pay 20 SUI"}
                              <AppLogoNetwork
                                network={network}
                                isBase
                                className="ml-[4px] h-[20px] w-[20px]"
                              />
                            </AppButton>
                          </button>
                          <div className="p-2 text-[12px] font-semibold text-orange-500">
                            50% OFF:{" "}
                            <span className="line-through">40 SUI</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {historySelected?.paymentInfo.type ===
                      PayType.STANDARD_PRICING && (
                      <div
                        className={`rounded-8 bg-white-50 flex h-[120px] w-full cursor-pointer flex-col justify-between gap-4 p-3 ${
                          historySelected.paymentInfo.type ===
                          PayType.STANDARD_PRICING
                            ? "border-white-700 border "
                            : ""
                        }`}
                      >
                        <div className="flex flex-col gap-4">
                          <AppButton
                            className="h-[28px] max-w-[138px]"
                            variant="secondary"
                          >
                            <StandardIcon />
                            Standard Pricing
                          </AppButton>
                        </div>
                        <button
                          type="submit"
                          disabled={
                            disableStandardPricing ||
                            isPaying ||
                            isSufficientBalance
                          }
                        >
                          <AppButton
                            className="h-[40px] w-full min-w-[225px] text-[14px] font-[500]"
                            disabled={
                              disableStandardPricing ||
                              isPaying ||
                              isSufficientBalance
                            }
                          >
                            {isPaying ? "Paying..." : "Pay 40 SUI"}{" "}
                            <AppLogoNetwork
                              network={network}
                              isBase
                              className="ml-[4px] h-[20px] w-[20px]"
                            />
                          </AppButton>
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="text-white-500 mt-3 text-[12px]">
                    ETA: Submission will be verified by RaidenX. Average
                    processing time after receiving payment is less than 1 hour.
                  </div>
                </>
              )}

              {historySelected.paymentInfo.tx && (
                <div className="rounded-10 mt-4">
                  <div className="text-white-1000 text-[12px] font-semibold">
                    2. Payment Information
                  </div>
                  <div className="flex flex-col gap-3">
                    <InputField
                      label="Transaction Hash"
                      value={historySelected.paymentInfo.tx || "-"}
                    />
                    <InputField
                      label="Payment Amount"
                      value={`${historySelected.paymentInfo.amount || "0"} SUI`}
                      prefix="Amount"
                    />
                    <InputField
                      label="Payer"
                      value={historySelected.paymentInfo.walletAddress || "-"}
                    />
                  </div>
                </div>
              )}
            </div>
            <div className="flex flex-col gap-4">
              <div className="bg-white-50 rounded-10 mt-4 h-max w-[380px] p-3">
                <div className="text-white-1000 border-white-100 flex items-center gap-2 border-b border-dashed py-2 text-start text-[14px] font-semibold leading-[1.5]">
                  <PreviewVerifyIcon />
                  Preview
                </div>
                <PreviewToken
                  previewData={{
                    ...historySelected,
                  }}
                  isMyTicket={true}
                />
              </div>
              <div className="rounded-10 bg-white-50 py-4">
                <div className="mb-2 px-4">History</div>
                <div className="flex">
                  {TAB.map((item: any, index) => {
                    return (
                      <div
                        onClick={() => setActiveTab(item.id)}
                        key={index}
                        className={`hover:text-neutral-alpha-1000 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] leading-[16px] hover:font-semibold ${
                          activeTab === item.id
                            ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b font-semibold"
                            : "text-neutral-alpha-800 border-0 font-normal"
                        }`}
                      >
                        {item.name}{" "}
                      </div>
                    );
                  })}
                </div>
                <div className="flex-1">{renderTab}</div>
              </div>
            </div>
          </div>
        </form>
      ) : (
        <div>
          <div className="text-white-1000 mt-10 text-center text-[18px] font-semibold">
            No ticket found. Please submit a new ticket.
          </div>
        </div>
      )}
    </>
  );
};

export default MyTickets;
