import BigNumber from "bignumber.js";
import { jwtDecode } from "jwt-decode";
import { isEmpty } from "lodash";
import Tooltip from "rc-tooltip";
import React, { useContext, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  CopyIcon,
  FireIcon,
  LinkIcon,
  StarIcon,
  Telegram,
  TwitterIcon,
  VerifyIcon,
  WarningIcon,
  WebsiteIcon,
  CheckboxIcon,
  CheckedIcon,
} from "@/assets/icons";
import {
  AppAvatarToken,
  AppCopy,
  AppLogoNetwork,
  AppNumber,
} from "@/components";
import { BREAKPOINT } from "@/enums/responsive.enum";
import { useFavouritePairs } from "@/hooks/useFavouritePairs";
import useWindowSize from "@/hooks/useWindowSize";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { ROUTE_PATH } from "@/routes";
import { RootState } from "@/store";
import { TPair, TPairPrice, TPairToken } from "@/types";
import { DEXS } from "@/utils/dex";
import {
  copyToClipboard,
  isReferralCodeDefault,
  multipliedBN,
} from "@/utils/helper";
import {
  getCirculatingSupply,
  getLinkSocialPair,
  getLinkWebsitePair,
  isTrendingPair,
} from "@/utils/pair";
import { useParams, useRouter } from "next/navigation";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import Image from "next/image";

interface PreviewTokenProps {
  previewData: any;
  isMyTicket?: boolean;
}

export const PreviewToken = ({
  previewData,
  isMyTicket = false,
}: PreviewTokenProps) => {
  const [tokenBase, setTokenBase] = useState<TPairToken | any>({});
  const [includeRefCode, setIncludeRefCode] = useState<boolean>(true);
  const [canVisibleBannerImageUrl, setCanVisibleBannerImageUrl] =
    useState<boolean>(false);
  const { windowWidth } = useWindowSize();
  const { pairPrice, pair } = useContext(RootPairContext) as {
    pairPrice: TPairPrice;
    pair: TPair;
  };
  const { isFavourite } = useFavouritePairs();
  const router = useRouter();
  // const imgBackground = isMyTicket
  //   ? previewData?.bannerImageUrl
  //   : previewData?.bannerImage instanceof Blob
  //     ? URL.createObjectURL(previewData.bannerImage)
  //     : tokenBase?.bannerImageUrl;
  const imgBackground =
    previewData?.bannerImage instanceof Blob
      ? URL.createObjectURL(previewData.bannerImage)
      : previewData?.bannerImageUrl;

  const imageTokenIcon =
    previewData?.logoImage instanceof Blob
      ? URL.createObjectURL(previewData?.logoImage)
      : previewData?.logoImageUrl;
  console.log("imageTokenIcon", imageTokenIcon, previewData);
  const referralMyCode = useSelector(
    (state: RootState) => state.user.referralMyCode
  );
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const linkWebsite = previewData?.website
    ? `${previewData?.website}`
    : getLinkWebsitePair(tokenBase?.socials?.websites);
  const linkTelegram = previewData?.telegram
    ? `https://t.me/${previewData?.telegram}`
    : getLinkSocialPair(tokenBase?.socials?.socials, "telegram");
  const linkTwitter =
    `https://twitter.com/${previewData?.twitter}` ||
    getLinkSocialPair(pair?.tokenBase?.socials?.socials, "twitter");

  const numberCols = [linkTelegram, linkTwitter, linkWebsite].filter(
    Boolean
  ).length;

  useEffect(() => {
    setTokenBase(pair?.tokenBase);
  }, [pair?.pairId]);

  const handleWhenSocialsChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.tokenAddress !== pair.tokenBase.address) {
      return;
    }

    setTokenBase({
      ...tokenBase,
      logoImageUrl: data.logoImageUrl,
      bannerImageUrl: data.bannerImageUrl,
      socials: {
        ...tokenBase.socials,
        websites: data.websites,
        socials: data.socials,
      },
    });
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
      handleWhenSocialsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
        handleWhenSocialsChange
      );
    };
  }, [pair.pairId]);

  const decodedInfo = useMemo(() => {
    if (accessToken) {
      return jwtDecode(accessToken) as any;
    }

    return {};
  }, [accessToken]);

  const isRefDefault = isReferralCodeDefault(
    referralMyCode,
    decodedInfo?.userId
  );

  const currentUrl = window.location.href;
  const newUrl = currentUrl.replace(/\/verify$/, "");

  const linkPairDetail = `${newUrl}${
    includeRefCode && accessToken && !isRefDefault
      ? `?ref=${referralMyCode}`
      : ""
  }`;

  const network = "sui";
  const [liquidityUsd, setLiquidityUsd] = useState("0");
  const [capUsd, setCapUsd] = useState("0");

  useEffect(() => {
    if (isEmpty(pair) || !pairPrice) return;
    setLiquidityUsd(pair.liquidityUsd);
    setCapUsd(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
  }, [pair?.pairId, pairPrice]);

  const totalSupply = pair?.tokenBase?.totalSupply;

  const ItemMetadata: React.FC<any> = ({
    title,
    content,
    decimals,
    isUSD,
    isNeedUpdate,
    info,
    isShowLogo,
  }) => {
    return (
      <div className="border-neutral-alpha-50 bg-neutral-alpha-50 flex h-[50px] flex-col items-center rounded-[4px] border px-[8px] py-[6px]">
        {!isEmpty(info) ? (
          <Tooltip overlay={info} placement="top">
            <div className="border-neutral-alpha-100 text-neutral-alpha-500 mb-[2px] w-max cursor-pointer border-b border-dashed text-[10px] uppercase leading-[1.6]">
              {title}
            </div>
          </Tooltip>
        ) : (
          <div className="text-neutral-alpha-500 mb-[2px] text-[10px] uppercase leading-[1.6]">
            {title}
          </div>
        )}

        <div
          className={`flex w-max items-center gap-1 font-semibold leading-[20px] transition-all duration-300 ${
            isNeedUpdate ? "bg-neutral-alpha-600" : "bg-transparent"
          }`}
        >
          {isShowLogo && (
            <AppLogoNetwork
              network={network}
              isBase
              className="mt-[-2px] h-[14px] w-[14px]"
            />
          )}

          <AppNumber
            className="font-semibold"
            value={content}
            decimals={decimals || 8}
            isForUSD={isUSD}
          />
        </div>
      </div>
    );
  };

  return (
    <>
      {" "}
      <div className="tablet:pt-3 pt-2">
        <div>
          <div className="max-tablet:justify-center tablet:mb-3 mb-2 flex items-center justify-between px-2.5">
            <div className="flex items-center gap-2">
              <div className="max-tablet:hidden">
                <AppAvatarToken className="h-10 w-10" image={imageTokenIcon} />
              </div>
              <div>
                <div className="tablet:flex mb-[2px] hidden items-center gap-1 text-[16px] font-medium leading-[24px]">
                  <div className="max-w-[160px] truncate">
                    {pair?.tokenBase?.symbol
                      ? pair?.tokenBase?.name
                      : "Unknown"}
                  </div>
                  <AppCopy
                    message={pair?.tokenBase?.address}
                    className="h-[14px] w-[14px]"
                  />
                  / {pair?.tokenQuote?.symbol}
                  {isTrendingPair(pair) && (
                    <div className="flex items-center text-[12px] leading-[20px] text-yellow-500">
                      <FireIcon /> #{pair?.trendingRank}
                    </div>
                  )}
                </div>
                <div className="text-neutral-alpha-800 flex items-center gap-2 text-[12px] font-medium leading-[18px]">
                  ${pair?.tokenBase?.symbol || "Unknown"}{" "}
                  <a
                    href={DEXS[pair?.dex?.dex as keyof typeof DEXS]?.website}
                    target="_blank"
                    className="text-neutral-alpha-500"
                  >
                    <div className="flex items-center gap-1">
                      <img
                        className="h-[16px] w-[16px] rounded-[100px]"
                        src={DEXS[pair?.dex?.dex as keyof typeof DEXS]?.logoUrl}
                        alt={DEXS[pair?.dex?.dex as keyof typeof DEXS]?.name}
                      />
                      <div className="text-[12px] leading-[18px]">
                        {DEXS[pair?.dex?.dex as keyof typeof DEXS]?.name ||
                          "Unknown"}
                      </div>
                      {pair?.dex?.version && (
                        <div className="text-neutral-alpha-300 text-[10px] leading-[16px]">
                          {pair?.dex?.version || ""}
                        </div>
                      )}
                    </div>
                  </a>
                </div>
              </div>
            </div>

            <div className="bg-neutral-alpha-100 hover:bg-neutral-alpha-50 max-tablet:hidden flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-[4px]">
              <StarIcon
                className={`h-[14px] w-[14px] ${
                  isFavourite(pair?.pairId) ? "text-yellow-500" : ""
                }`}
              />
            </div>
          </div>

          {new BigNumber(pair.liquidityUsd).comparedTo(100) < 0 && (
            <div className="body-sm-regular-12 mx-2.5 mb-3 flex items-center gap-2 rounded-[4px] bg-yellow-900 px-2 py-1 text-yellow-500">
              <WarningIcon className="h-4 w-4 text-yellow-500  " />
              This pair has very little liquidity
            </div>
          )}
        </div>

        <>
          {imgBackground && (
            <div
              className="cursor-pointer overflow-hidden"
              style={{
                background:
                  "linear-gradient(180deg, rgba(0, 0, 0, 0.30) -7.03%, rgba(0, 0, 0, 0.00) 14.38%, rgba(0, 0, 0, 0.00) 77.22%, rgba(0, 0, 0, 0.30) 100%)",
                height: canVisibleBannerImageUrl ? "auto" : 0,
              }}
            >
              <img
                src={imgBackground}
                onError={() => setCanVisibleBannerImageUrl(false)}
                onLoad={() => setCanVisibleBannerImageUrl(true)}
                className="aspect-[6/2] w-full object-cover transition-all duration-700 hover:scale-110"
              />
            </div>
          )}

          <div
            className={`mx-2.5 flex rounded-[4px]  ${
              imgBackground && canVisibleBannerImageUrl ? "mt-[-14px]" : ""
            }`}
            style={{
              position: "relative",
              backdropFilter: "blur(7.5px)",
              background: `linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 100%), rgba(6, 7, 14, ${
                windowWidth <= BREAKPOINT.DESKTOP ? "0.94" : "0.8"
              })`,
            }}
          >
            <div
              className={`grid w-[calc(100%-40px)] flex-1 grid-cols-${
                numberCols + 2
              }`}
            >
              {linkWebsite && (
                <a href={linkWebsite} target="_blank">
                  <div className="hover:bg-neutral-alpha-50 border-neutral-beta-500 flex h-full items-center justify-center gap-1 rounded-r-[6px] border-r py-2 text-[12px] leading-[18px]">
                    <WebsiteIcon />
                    {/* Website */}
                  </div>
                </a>
              )}

              {linkTwitter && (
                <a href={linkTwitter} target="_blank">
                  <div className="hover:bg-neutral-alpha-50 border-neutral-beta-500 flex h-full items-center justify-center gap-1 rounded-r-[6px] border-r py-2 text-[12px] leading-[18px]">
                    <TwitterIcon className="h-4 w-4" />
                    {/* Twitter */}
                  </div>
                </a>
              )}

              {linkTelegram && (
                <a href={linkTelegram} target="_blank">
                  <div className="hover:bg-neutral-alpha-50 border-neutral-beta-500 flex h-full items-center justify-center gap-1 rounded-r-[6px] border-r py-2 text-[12px] leading-[18px]">
                    <Telegram className="h-4 w-4" />
                    {/* Telegram */}
                  </div>
                </a>
              )}

              <Tooltip
                showArrow={false}
                overlayClassName="tooltip-marker p-0 opacity-100"
                overlay={
                  <div className="max-w-[240px] p-2">
                    <div className="text-white-0 mb-[6px] text-[12px] font-medium">
                      Share this token link along with your referral code
                    </div>
                    <div className="body-sm-regular-12 text-white-0 flex items-center gap-[8px]">
                      <div
                        onClick={() =>
                          isRefDefault || !accessToken
                            ? undefined
                            : setIncludeRefCode(!includeRefCode)
                        }
                        className="cursor-pointer"
                      >
                        {isRefDefault || !accessToken ? (
                          <div className="bg-white-150 h-[16px] w-[16px] cursor-not-allowed rounded-[4px]" />
                        ) : includeRefCode ? (
                          <Image
                            src={CheckedIcon}
                            width={16}
                            height={16}
                            alt="Checked icon"
                            unoptimized
                          />
                        ) : (
                          <Image
                            src={CheckboxIcon}
                            width={16}
                            height={16}
                            alt="Checked icon"
                            unoptimized
                          />
                        )}
                      </div>
                      Include your referral code
                    </div>
                    {(isRefDefault || !accessToken) && (
                      <div className="mt-[6px]">
                        <div className="text-white-800 text-[10px]">
                          You haven&#39;t generated your referral code.
                        </div>
                        <div
                          className="text-brand-500 cursor-pointer text-[10px] font-medium"
                          onClick={() => router.push(ROUTE_PATH.REFERRALS)}
                        >
                          Make one here
                        </div>
                      </div>
                    )}

                    <button
                      onClick={() => copyToClipboard(linkPairDetail)}
                      className="text-black-900 hover:bg-white-900 rounded-6 bg-white-0 mt-[8px] flex h-[32px] w-full items-center justify-center gap-1 whitespace-nowrap p-[8px] text-[12px] font-medium"
                    >
                      <CopyIcon className="w-4" />
                      Copy Link
                    </button>
                  </div>
                }
                placement="bottom"
              >
                <div className="hover:bg-neutral-alpha-50 rounded-r-[6px]">
                  <div className="border-neutral-beta-500 flex h-full items-center justify-center gap-1 rounded-r-[6px] border-r py-2 text-[12px] leading-[18px]">
                    <LinkIcon className="h-4 w-4" />
                  </div>
                </div>
              </Tooltip>
              <div>
                <div className="hover:bg-neutral-alpha-50 border-neutral-beta-500 flex h-full cursor-pointer items-center justify-center gap-1 rounded-r-[6px] border-r py-2 text-[12px] leading-[18px]">
                  {linkTelegram && linkTwitter && linkWebsite ? (
                    <VerifyIcon className="h-4 w-4" />
                  ) : (
                    <>
                      <span className="text-brand-500 text-[12px]">Update</span>{" "}
                      <VerifyIcon className="h-4 w-4" />
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </>
      </div>
      <div>
        <div className="mt-3 flex gap-[6px]">
          <div className="flex-1">
            <div className="grid grid-cols-2 gap-1">
              <ItemMetadata
                title="Price USD"
                content={pairPrice?.priceUsd}
                decimals={pair?.tokenBase?.decimals}
                isUSD={!!+pairPrice?.priceUsd}
              />
              <ItemMetadata
                isShowLogo
                title={`Price ${pair?.tokenQuote?.symbol || ""}`}
                content={pairPrice?.price}
                decimals={pair?.tokenBase?.decimals}
              />
            </div>
            <div className="mt-1 grid grid-cols-3 gap-1">
              <ItemMetadata
                title="Liquidity"
                content={liquidityUsd}
                decimals={pair?.tokenBase?.decimals}
                isUSD={
                  !!+multipliedBN(pair?.liquidity, pair?.tokenQuote?.priceUsd)
                }
              />
              <ItemMetadata
                title="FDV"
                content={
                  Number(totalSupply || 0) * Number(pairPrice?.priceUsd || 0)
                }
                decimals={pair?.tokenBase?.decimals}
                isUSD
                info={
                  <div className="text-center">
                    Fully diluted valuation:
                    <br />
                    (total supply * price)
                  </div>
                }
              />
              <ItemMetadata
                title="MKT Cap"
                content={capUsd}
                decimals={2}
                isUSD={!!+capUsd}
                info={
                  !pair.tokenBase?.circulatingSupply ? (
                    <div className="text-center">
                      MKT Cap valuation: <br /> (total supply - burnt supply) *
                      price
                    </div>
                  ) : null
                }
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
