"use client";

import { default as React, useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { FlashIcon, SettingsIcon } from "@/assets/icons";
import { NumericFormat } from "react-number-format";
import { AppLogoNetwork } from "@/components";
import { debounce } from "lodash";
import { ModalQuickBuySettings } from "@/modals";
import { isZero } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import { useSettingsOrder } from "@/hooks";

export const BoxQuickBuy = ({
  buyAmount,
  setBuyAmount,
}: {
  buyAmount: any;
  setBuyAmount: (value: any) => void;
}) => {
  const [isClient, setIsClient] = useState(false);
  const [isOpenSettings, setIsOpenSettings] = useState<boolean>(false);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );

  const { updateSettingsQuickOrder, settingsQuickOrder } = useSettingsOrder();

  const debounceUpdate = useCallback(
    debounce(
      (nextValue: string) =>
        updateSettingsQuickOrder({
          ...settingsQuickOrder,
          buyQuickAmount: +nextValue,
        }),
      1000
    ),
    [settingsQuickOrder]
  );

  useEffect(() => {
    if (!isZero(settingsQuickOrder?.buyQuickAmount || 0)) {
      setBuyAmount(settingsQuickOrder?.buyQuickAmount || 0);
    }
  }, [settingsQuickOrder]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <div className="flex flex-1 items-center justify-end gap-2">
      <div className="border-brand-600 bg-brand-900 flex h-[32px] w-auto items-center justify-end rounded-[6px] border py-[2px] pr-[3px] md:h-[42px]">
        <div className="text-brand-500 body-sm-medium-12 flex items-center gap-[2px] p-[8px]">
          <FlashIcon />
          <span className="hidden md:block">Quick buy</span>
        </div>
        <div className="bg-black-900 flex h-full flex-1 items-center gap-[2px] rounded-[6px] px-2 md:p-[8px]">
          <NumericFormat
            disabled={!accessToken && !isExternalWallet}
            value={buyAmount}
            onChange={(e) => {
              setBuyAmount(e.target.value.replace(/,/g, ""));
              if (!accessToken) return;
              debounceUpdate(e.target.value.replace(/,/g, "") || "");
            }}
            thousandSeparator=","
            valueIsNumericString
            decimalScale={6}
            className="body-md-regular-14 w-[30px] max-w-[68px] bg-transparent outline-none md:w-[34px]"
          />
          <AppLogoNetwork network={NETWORKS.SUI} isBase />
        </div>
      </div>

      {accessToken && (
        <SettingsIcon
          className={`h-[16px] w-[16px] ${
            accessToken ? "cursor-pointer" : "cursor-not-allowed"
          } text-white-1000`}
          onClick={() => {
            if (!accessToken) return;
            setIsOpenSettings(true);
          }}
        />
      )}

      {isOpenSettings && (
        <ModalQuickBuySettings
          isOpen={isOpenSettings}
          onClose={() => setIsOpenSettings(false)}
        />
      )}
    </div>
  );
};
