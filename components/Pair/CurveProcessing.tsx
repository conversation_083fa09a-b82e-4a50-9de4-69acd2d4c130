import React, { useContext } from "react";
import { isDexHasBondingCurve } from "@/utils/dex";
import { formatToPercent } from "@/utils/format";
import { TPair } from "@/types";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";

const CurveProcessing = () => {
  const {
    pair,
    // reserveQuote,
    // reserveBase
  } = useContext(RootPairContext) as {
    pair: TPair;
    reserveQuote: string;
    reserveBase: string;
  };

  if (isDexHasBondingCurve(pair?.dex?.dex)) {
    const getPercent = () => {
      return formatToPercent(pair?.bondingCurve).toString();
    };

    return (
      <div className="border-white-50 border-b px-[8px] py-[12px]">
        <div className="body-xs-regular-10 mb-[12px]">
          Bonding Curve Progress:{" "}
          <span className="body-xs-medium-10">{getPercent()}</span>
        </div>
        <div className="bg-white-50 h-[4px] w-full rounded-[100px]">
          <div
            className={`h-[4px] rounded-[100px]`}
            style={{
              background: "linear-gradient(90deg, #80B2FF 0%, #0FC 100%)",
              width: getPercent(),
            }}
          />
        </div>
      </div>
    );
  }
  return <></>;
};
export default CurveProcessing;
