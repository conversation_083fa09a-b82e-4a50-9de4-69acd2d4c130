"use client";

import {
  default as React,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { OrderFormBuy } from "./buy-order";
import { OrderFormSell } from "./sell-order";
import { TRADE_TYPE } from "@/enums/trade.enum";
import clsx from "clsx";
import { BuyIcon, TagIcon, WalletIcon } from "@/assets/icons";
import { AppNumber } from "../AppNumber";
import { AppAvatarTokenQuote } from "@/components";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { TPair } from "@/types/pair.type";
import {
  getCoinBalanceOnchain,
  getSuiBalanceOnchain,
} from "../../utils/suiClient";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { convertMistToDec } from "@/utils/helper";
import { SUI_DECIMALS } from "@/utils/contants";

export const OrderFormExternalWallet = () => {
  const [type, setType] = useState<number>(TRADE_TYPE.BUY);
  const [balanceSui, setBalanceSui] = useState<string>("");
  const [balanceToken, setBalanceToken] = useState<string>("");
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };

  const currentAccount = useCurrentAccount();

  const _renderContentOrder = () => {
    if (type === TRADE_TYPE.BUY) {
      return (
        <OrderFormBuy
          balanceToken={balanceToken}
          balanceSui={balanceSui}
          fetchBalance={fetchBalance}
        />
      );
    }
    return (
      <OrderFormSell balanceToken={balanceToken} fetchBalance={fetchBalance} />
    );
  };

  const getBalanceSui = useCallback(async () => {
    const res = await getSuiBalanceOnchain(currentAccount?.address || "");
    setBalanceSui(convertMistToDec(res, SUI_DECIMALS));
  }, [currentAccount?.address]);

  const getBalanceToken = useCallback(async () => {
    const res = await getCoinBalanceOnchain(
      currentAccount?.address || "",
      pair?.tokenBase?.address
    );
    setBalanceToken(convertMistToDec(res, pair?.tokenBase?.decimals));
  }, [currentAccount?.address]);

  const fetchBalance = async () => {
    getBalanceSui().then();
    getBalanceToken().then();
  };

  useEffect(() => {
    if (!currentAccount?.address) return;
    getBalanceSui().then();
  }, [currentAccount?.address]);

  useEffect(() => {
    if (!currentAccount?.address || !pair?.tokenBase?.address) return;
    getBalanceToken().then();
  }, [currentAccount?.address, pair?.tokenBase?.address]);

  return (
    <div
      className={clsx(
        "tablet:h-auto bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden tablet:max-h-full flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        "pr-0"
      )}
    >
      <div className="w-full">
        <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
          <div className="flex items-center gap-2">
            <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
              <WalletIcon />
            </div>
            <div className="flex gap-2">
              <div className="body-sm-semibold-12">Wallet</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <AppNumber
              value={balanceSui}
              className="text-white-800 body-sm-regular-12 pr-1"
            />
            <AppAvatarTokenQuote pair={pair} />
          </div>
        </div>
        <div className="tablet:grid bg-neutral-beta-500 mb-[8px] mt-[12px] hidden grid-cols-2 rounded-[4px] p-[4px]">
          <div
            className={`${
              type === TRADE_TYPE.BUY
                ? "bg-white-100 text-white-1000"
                : "text-neutral-alpha-500"
            } body-sm-semibold-14 flex cursor-pointer items-center justify-center gap-1 rounded-[4px] px-[8px] py-[6px] text-center`}
            onClick={() => setType(TRADE_TYPE.BUY)}
          >
            <BuyIcon /> Buy
          </div>
          <div
            className={`${
              type === TRADE_TYPE.SELL
                ? "bg-white-100 text-white-1000"
                : "text-neutral-alpha-500"
            } body-sm-semibold-14 flex cursor-pointer items-center justify-center gap-1 rounded-[4px] px-[8px] py-[6px] text-center`}
            onClick={() => setType(TRADE_TYPE.SELL)}
          >
            <TagIcon /> Sell
          </div>
        </div>
        <div>{_renderContentOrder()}</div>
      </div>

      <div
        className="tablet:hidden bg-neutral-alpha-100 ml-4 flex w-[36px] rotate-180 items-center justify-center gap-2 rounded-br-md rounded-tr-md text-center font-medium"
        style={{ writingMode: "vertical-rl" }}
        onClick={() =>
          setType(type === TRADE_TYPE.BUY ? TRADE_TYPE.SELL : TRADE_TYPE.BUY)
        }
      >
        {type === TRADE_TYPE.BUY ? (
          <TagIcon className="rotate-90" />
        ) : (
          <BuyIcon className="rotate-90" />
        )}
        {type === TRADE_TYPE.BUY ? "Sell" : "Buy"}
      </div>
    </div>
  );
};
