"use client";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import * as React from "react";
import { useState } from "react";
import { OrderFormType } from "@/enums";
import { useContext } from "react";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { TPair } from "@/types/pair.type";
import { AppSymbolToken } from "@/components/AppSymbolToken";
import { AppNumber } from "@/components/AppNumber";
import { AmountForm } from "./AmountForm";
import { AppButton } from "../../AppButton";
import { BoxConnectTelegram } from "../BoxConnectTelegram";
import { useExternalWallet } from "@/hooks";
import BigNumber from "bignumber.js";
import { toastSuccess } from "@/libs/toast";
import { getLinkTxExplorer } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import { useRef } from "react";
import { useCallback } from "react";
import { debounce } from "lodash";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { useEffect } from "react";
import { simulateBuyExactIn } from "@/utils/simulates";
import { formatNumber } from "@/utils/format";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";

export const OrderFormBuy = ({
  balanceToken,
  balanceSui,
  fetchBalance,
}: {
  balanceToken: string;
  balanceSui: string;
  fetchBalance: () => void;
}) => {
  const [orderType, setOrderType] = useState<string>(OrderFormType.MARKET);
  const [amount, setAmount] = useState<any>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const tokenAmountEstimateRef = useRef<any>("");
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");

  const currentAccount = useCurrentAccount();
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const { onBuyToken } = useExternalWallet();

  const onBuySuccess = async (digest?: string) => {
    await fetchBalance();
    toastSuccess("Success", `You bought ${amount} ${pair?.tokenBase?.symbol}`, {
      link: getLinkTxExplorer(NETWORKS.SUI, digest || ""),
      text: "View Explorer",
    });
    setIsLoading(false);
    setAmount("");
  };

  const onBuy = () => {
    onBuyToken(pair, amount, setIsLoading, onBuySuccess);
  };

  const debouncedEstimateQuickBuy = useCallback(
    debounce(async (pair: TPair, buyAmount: string) => {
      const pool = {
        dex: pair.dex.dex as any,
        objectId: pair.poolId,
      };

      const amountOut = await simulateBuyExactIn(
        currentAccount?.address || "",
        buyAmount,
        pair?.tokenQuote,
        pair?.tokenBase,
        pool,
        true,
        pair?.isXQuoteToken,
        pair?.feeTier
      );

      if (amountOut) {
        tokenAmountEstimateRef.current = amountOut;
        setTokenAmountEstimate(tokenAmountEstimateRef.current);
      }
    }, 1500),
    [currentAccount?.address, pair?.pairId]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (
      !currentAccount?.address ||
      !+amount ||
      !+balanceSui ||
      new BigNumber(amount).gt(balanceSui)
    )
      return;
    debouncedEstimateQuickBuy(pair, amount);
    return () => {
      debouncedEstimateQuickBuy.cancel();
    };
  }, [currentAccount?.address, amount, pair?.pairId, balanceSui]);

  const _renderButton = () => {
    if (!+balanceSui || new BigNumber(amount).gt(balanceSui)) {
      return <AppButton size="large">Insufficient Balance</AppButton>;
    }

    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    return (
      <AppButton
        disabled={!+amount || isLoading}
        variant="buy"
        size="large"
        onClick={onBuy}
        className="h-[40px] flex-col items-center py-0"
      >
        Buy {!!+amount && `${amount} SUI`}
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈
            {formatNumber(tokenAmountEstimate, pair?.tokenBase.decimals)}{" "}
            {pair?.tokenBase?.symbol})
          </div>
        )}
      </AppButton>
    );
  };

  return (
    <div>
      <div className="tablet:mt-0 mt-4 flex gap-4">
        <div className="flex w-full items-center justify-end gap-2">
          <div className="border-neutral-alpha-50 flex flex-1 gap-3 border-b pb-1">
            {OPTIONS_ORDER_TYPE.map((item, index) => {
              return (
                <div
                  key={index}
                  onClick={() => {
                    setOrderType(item.value);
                  }}
                  className={`mb-[-4px] cursor-pointer
                   ${
                     orderType === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-sm-semibold-12 border-b"
                       : "text-white-500 action-xs-medium-12"
                   }`}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          <div className="flex items-center gap-2">
            <div className="body-sm-regular-12 text-white-500 flex">
              <AppNumber value={balanceToken} className="text-white-800 pr-1" />
              <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
            </div>
          </div>
        </div>
      </div>
      {orderType !== OrderFormType.MARKET ? (
        <BoxConnectTelegram orderType={orderType} />
      ) : (
        <>
          <AmountForm setAmount={setAmount} amount={amount} />
          <div className="mt-4">{_renderButton()}</div>
        </>
      )}
    </div>
  );
};
