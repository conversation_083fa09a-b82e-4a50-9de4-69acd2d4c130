"use client";

import { NumericFormat } from "react-number-format";
import { isZero } from "@/utils/helper";
import * as React from "react";
import { formatNumberWithCommas } from "@/utils/format";
import { useEffect, useRef, useState } from "react";
import { useMediaQuery } from "react-responsive";
import Storage from "@/libs/storage";
import { TPair } from "@/types";
import { useContext } from "react";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { AppAvatarTokenQuote } from "@/components";

export const AmountForm = ({
  amount,
  setAmount,
}: {
  amount: any;
  setAmount: (value: any) => void;
}) => {
  const [isClient, setIsClient] = useState(false);
  const amountRef = useRef<any>("0");
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const orderSettings = Storage.getOrderSettings();
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFocusInputAmount = () => {
    if (!isClient) return;
    const formattedAmount = formatNumberWithCommas(amount);
    amountRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };

  if (!isClient) return null;

  return (
    <>
      <div className="tablet:border border-neutral-alpha-50 tablet:rounded-[8px] mt-3">
        <div className="bg-neutral-beta-800 tablet:rounded-t-[8px] tablet:border-0 border-white-50 flex items-center gap-2 rounded-[4px] border p-2 ">
          <div className="body-sm-regular-12 text-neutral-alpha-800">
            Amount
          </div>
          <NumericFormat
            getInputRef={amountRef}
            value={amount ?? ""}
            autoFocus={!isMobile}
            onFocus={handleFocusInputAmount}
            allowLeadingZeros
            allowNegative={false}
            thousandSeparator=","
            className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
            decimalScale={pair?.tokenQuote?.decimals}
            onValueChange={({ floatValue }) => {
              if (!isZero(floatValue)) {
                setAmount(floatValue?.toString());
              } else {
                setAmount("");
              }
            }}
          />

          <div className="text-neutral-alpha-500">
            <AppAvatarTokenQuote pair={pair} />
          </div>
        </div>

        <div className="tablet:gap-0 tablet:mt-0 mt-[4px] grid h-[34px] grid-cols-4 gap-1">
          {orderSettings?.defaultBuyAmount?.map((item, index) => {
            return (
              <div
                onClick={() => setAmount(item)}
                key={index}
                className={`g tablet:rounded-[0px] tablet:border-0 border-neutral-alpha-100 hover:bg-neutral-alpha-50 flex cursor-pointer items-center justify-center rounded-[4px] border py-[4px] text-center ${
                  index + 1 === 4
                    ? ""
                    : "tablet:border-r border-neutral-alpha-50"
                } ${
                  +amount === item
                    ? "bg-neutral-alpha-50"
                    : "body-md-regular-14"
                }`}
              >
                {item}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};
