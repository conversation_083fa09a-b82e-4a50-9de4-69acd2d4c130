import { NumericFormat } from "react-number-format";
import { isZero, toStringBN } from "@/utils/helper";
import * as React from "react";
import { useContext, useRef } from "react";
import { TPair } from "@/types";
import { formatNumberWithCommas } from "@/utils/format";
import Storage from "@/libs/storage";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";

export const AmountForm = ({
  sellPercent,
  setSellPercent,
}: {
  setSellPercent: (value: any) => void;
  sellPercent: any;
}) => {
  const inputRef = useRef<any>(null);
  const orderSettings = Storage.getOrderSettings();
  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };

  const handleFocusInputAmount = () => {
    const formattedAmount = formatNumberWithCommas(sellPercent);
    inputRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };

  return (
    <div className="tablet:border border-neutral-alpha-50 tablet:rounded-[8px] mt-3 overflow-hidden">
      <div className="bg-neutral-beta-800 tablet:rounded-[0px] tablet:border-0 border-white-50 flex items-center gap-2 rounded-[4px] border p-2">
        <div className="body-sm-regular-12 text-neutral-alpha-800">Amount</div>
        <NumericFormat
          getInputRef={inputRef}
          value={sellPercent ?? ""}
          allowLeadingZeros
          onFocus={handleFocusInputAmount}
          allowNegative={false}
          thousandSeparator=","
          className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
          decimalScale={pair?.tokenBase?.decimals}
          onValueChange={({ floatValue }) => {
            if (!isZero(floatValue)) {
              return setSellPercent(toStringBN(floatValue));
            } else {
              setSellPercent("");
            }
          }}
        />

        <div className="text-neutral-alpha-500 body-xs-regular-10">%</div>
      </div>

      <div className="tablet:gap-0 tablet:mt-0 mt-[4px] grid h-[34px] flex-1 grid-cols-4 gap-1">
        {orderSettings.defaultSellPercent.map((item, index) => {
          return (
            <div
              onClick={() => setSellPercent(item)}
              key={index}
              className={`tablet:rounded-[0px] tablet:border-0 border-neutral-alpha-100 hover:bg-neutral-alpha-50 flex cursor-pointer items-center justify-center rounded-[4px] border py-[4px] text-center
                  ${
                    index + 1 === 4
                      ? ""
                      : "tablet:border-r border-neutral-alpha-50"
                  }
                  ${
                    +sellPercent === item
                      ? "bg-neutral-alpha-50 action-xs-medium-12"
                      : "body-sm-regular-12"
                  }`}
            >
              {item}%
            </div>
          );
        })}
      </div>
    </div>
  );
};
