"use client";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import * as React from "react";
import { useMemo, useState } from "react";
import { OrderFormType } from "@/enums";
import { useContext } from "react";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { TPair } from "@/types";
import { AmountForm } from "./AmountForm";
import { AppButton, AppNumber, AppSymbolToken } from "@/components";
import BigNumber from "bignumber.js";
import { dividedBN, getLinkTxExplorer } from "@/utils/helper";
import { useExternalWallet } from "@/hooks";
import { toastSuccess } from "@/libs/toast";
import { NETWORKS } from "@/utils/contants";
import { useRef } from "react";
import { useCallback } from "react";
import { debounce } from "lodash";
import { useEffect } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { simulateSellExactIn } from "@/utils/simulates";
import { formatNumber } from "@/utils/format";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";
import { BoxConnectTelegram } from "../BoxConnectTelegram";

export const OrderFormSell = ({
  balanceToken,
  fetchBalance,
}: {
  balanceToken: string;
  fetchBalance: () => void;
}) => {
  const [orderType, setOrderType] = useState<string>(OrderFormType.MARKET);
  const [sellPercent, setSellPercent] = useState<any>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const tokenAmountEstimateRef = useRef<any>("");
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");

  const currentAccount = useCurrentAccount();

  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
  };
  const { onSellToken } = useExternalWallet();

  const sellAmount = useMemo(
    () => new BigNumber(dividedBN(sellPercent, 100)).multipliedBy(balanceToken),
    [balanceToken, sellPercent]
  );

  const debouncedEstimateQuickSell = useCallback(
    debounce(async (pair: TPair, sellPercent: string) => {
      const pool = {
        dex: pair.dex.dex as any,
        objectId: pair.poolId,
      };

      const amountOut = await simulateSellExactIn(
        currentAccount?.address || "",
        +sellPercent,
        pair?.tokenBase,
        pair?.tokenQuote,
        pool,
        pair?.feeTier,
        pair.isXQuoteToken
      );
      if (amountOut) {
        tokenAmountEstimateRef.current = amountOut;
        setTokenAmountEstimate(tokenAmountEstimateRef.current);
      }
    }, 1500),
    [currentAccount?.address, pair?.pairId]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (
      !currentAccount?.address ||
      !sellPercent ||
      !+balanceToken ||
      new BigNumber(sellPercent).gt(100)
    )
      return;
    debouncedEstimateQuickSell(pair, sellPercent);
    return () => {
      debouncedEstimateQuickSell.cancel();
    };
  }, [currentAccount?.address, sellPercent, pair?.pairId, balanceToken]);

  const onSellSuccess = async (digest?: string) => {
    await fetchBalance();
    toastSuccess(
      "Success",
      `You sold ${sellAmount} ${pair?.tokenBase?.symbol}`,
      {
        link: getLinkTxExplorer(NETWORKS.SUI, digest || ""),
        text: "View Explorer",
      }
    );
    setIsLoading(false);
    setSellPercent("");
  };

  const onSell = () => {
    onSellToken(pair, sellPercent, setIsLoading, onSellSuccess).then();
  };

  const _renderButton = () => {
    if (!+balanceToken || new BigNumber(sellPercent).gt(100)) {
      return <AppButton size="large">Insufficient Balance</AppButton>;
    }

    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    return (
      <AppButton
        onClick={onSell}
        disabled={!+sellPercent || isLoading}
        variant="sell"
        size="large"
        className="h-[40px] flex-col items-center py-0"
      >
        Sell {!!+sellAmount && `${sellAmount} ${pair?.tokenBase?.symbol}`}
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈
            {formatNumber(tokenAmountEstimate, pair?.tokenQuote.decimals)}{" "}
            {pair?.tokenQuote?.symbol})
          </div>
        )}
      </AppButton>
    );
  };

  return (
    <div>
      <div className="tablet:mt-0 mt-4 flex gap-4">
        <div className="flex w-full items-center justify-end gap-2">
          <div className="border-neutral-alpha-50 flex flex-1 gap-3 border-b pb-1">
            {OPTIONS_ORDER_TYPE.map((item, index) => {
              return (
                <div
                  key={index}
                  onClick={() => {
                    setOrderType(item.value);
                  }}
                  className={`mb-[-4px] cursor-pointer
                   ${
                     orderType === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-sm-semibold-12 border-b"
                       : "text-white-500 action-xs-medium-12"
                   }`}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          <div className="flex items-center gap-2">
            <div className="body-sm-regular-12 text-white-500 flex">
              <AppNumber value={balanceToken} className="text-white-800 pr-1" />
              <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
            </div>
          </div>
        </div>
      </div>

      {orderType !== OrderFormType.MARKET ? (
        <BoxConnectTelegram orderType={orderType} />
      ) : (
        <>
          <AmountForm
            sellPercent={sellPercent}
            setSellPercent={setSellPercent}
          />

          <div className="mt-4">{_renderButton()}</div>
        </>
      )}
    </div>
  );
};
