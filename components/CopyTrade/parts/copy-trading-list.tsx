import Tooltip from "rc-tooltip";
import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  ChevronDownIcon,
  InfoIcon,
  PauseIcon,
  RemoveSnipeIcon,
  StartIcon,
} from "@/assets/icons";
import { AppCopy, AppDataTable } from "@/components";
import { AutoSellDetail } from "@/components/Snipe/auto-sell-details";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalRemoveSnipe } from "@/modals/ModalRemoveSnipe";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import {
  EBuyStrategy,
  ESellStrategy,
  TCopyTradeTarget,
} from "@/types/copytrade.type";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { NETWORKS } from "@/utils/contants";

interface CopyTradeItemPropTypes {
  target: TCopyTradeTarget;
  index: number;
}

const CopyTradeItem = React.memo(
  ({ target, index }: CopyTradeItemPropTypes) => {
    const [isOpenModalRemove, setIsOpenModalRemove] = useState<boolean>(false);
    const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
    const [isShowDetails, setIsShowDetails] = useState<boolean>(false);

    const handleDeleteTarget = async () => {
      try {
        await rf.getRequest("CopyTradeRequest").deleteTarget({
          network: NETWORKS.SUI,
          walletAddress: target.walletAddress,
          targetWalletAddress: target.targetWalletAddress,
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE);
        toastSuccess("Success", "Delete liquidity sniping successfully!");
        setIsOpenModalRemove(false);
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const handlePauseTarget = async () => {
      try {
        await rf.getRequest("CopyTradeRequest").updateTarget({
          network: NETWORKS.SUI,
          walletAddress: target.walletAddress,
          targetWalletAddress: target.targetWalletAddress,
          data: {
            targetWalletName: target.targetWalletName,
            isActive: !target.isActive,
            buySettings: target.buySettings,
            sellSettings: target.sellSettings,
          },
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE);
        toastSuccess(
          "Success",
          `${target.isActive ? "Pause" : "Enable"} copy trading successfully!`
        );
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const handleUpdateAutoSell = async () => {
      const isActive = !target.autoSellSettings?.isActive;
      try {
        await rf.getRequest("CopyTradeRequest").updateTarget({
          network: NETWORKS.SUI,
          walletAddress: target.walletAddress,
          targetWalletAddress: target.targetWalletAddress,
          data: {
            ...target,
            autoSellSettings: {
              ...target.autoSellSettings,
              isActive,
            },
          },
        });
        AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE);
      } catch (error: any) {
        toastError("Error", error.message || "Something went wrong!");
      }
    };

    const _renderCopyBuy = () => {
      if (target?.buySettings === null) return "--";
      if (target?.buySettings?.buyStrategy === EBuyStrategy.BUY_FIXED_AMOUNT) {
        return (
          <div className="flex w-full items-start gap-1">
            <div className="text-white-500">{isMobile && `Buy`} Fixed:</div>
            <div className="text-white-1000">
              {formatNumber(target?.buySettings?.buyFixedAmount || 0, 8, "0")}{" "}
              SUI
            </div>
          </div>
        );
      }
      if (
        target?.buySettings?.buyStrategy === EBuyStrategy.BUY_PERCENT_AMOUNT
      ) {
        return (
          <div className="flex w-full items-start gap-1">
            <div className="text-white-500">{isMobile && `Buy`} Percent:</div>
            <div className="text-white-1000">
              {formatNumber(target?.buySettings?.buyPercentAmount || 0, 2, "0")}
              %
            </div>
          </div>
        );
      }
      return <div className="text-white-500">{isMobile && `Buy`} Exact</div>;
    };
    const _renderCopySell = () => {
      if (target?.sellSettings === null) return "--";
      if (
        target?.sellSettings?.sellStrategy === ESellStrategy.SELL_FIXED_AMOUNT
      ) {
        return (
          <div className="flex w-full items-start gap-1">
            <div className="text-white-500">{isMobile && `Sell`} Fixed:</div>
            <div className="text-white-1000">
              {formatNumber(target?.sellSettings?.sellFixedAmount || 0, 8, "0")}
              %
            </div>
          </div>
        );
      }
      if (
        target?.sellSettings?.sellStrategy === ESellStrategy.SELL_PERCENT_AMOUNT
      ) {
        return (
          <div className="flex w-full items-start gap-1">
            <div className="text-white-500">{isMobile && `Sell`} Percent:</div>
            <div className="text-white-1000">
              {formatNumber(
                target?.sellSettings?.sellPercentAmount || 0,
                2,
                "0"
              )}
              %
            </div>
          </div>
        );
      }
      return <div className="text-white-500">{isMobile && `Sell`} Exact</div>;
    };

    if (isMobile) {
      return (
        <div className="border-white-150 bg-white-25 w-full rounded-[4px] border p-2.5">
          <div className="border-white-100 flex justify-between border-b border-dashed pb-2">
            <div className="flex flex-col gap-[4px]">
              <div className="flex items-center gap-1 rounded-[2px] px-1">
                <div className="text-white-500 text-[10px]">Copy Wallet:</div>
                <div className="text-white-900 flex items-center gap-1 text-[10px]">
                  {formatShortAddress(target.targetWalletAddress, 5, 3)}{" "}
                  <AppCopy message={target.targetWalletAddress} />
                </div>
              </div>
              <div className="flex items-center gap-1 rounded-[2px] px-1">
                <div className="body-xs-regular-10 text-white-500">Name</div>
                <div className="flex items-center gap-1">
                  {target?.targetWalletName}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center gap-1">
              {_renderCopyBuy()}
              {_renderCopySell()}
            </div>
          </div>
          {isShowDetails && (
            <div className="border-white-50 mt-2 border-b border-dashed pb-2">
              <div className="flex items-center justify-between py-1">
                <div className="body-xs-regular-10 text-white-500">
                  Wallet address
                </div>
                <div className="flex items-center gap-1">
                  {formatShortAddress(target?.walletAddress, 5, 3)}
                  <AppCopy message={target?.walletAddress} />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="body-xs-regular-10 text-white-500">Status</div>
                <div
                  className={`rounded-4 flex items-center justify-center px-[4px] py-[1px] ${
                    target?.isActive
                      ? "bg-green-800 text-green-500"
                      : "bg-yellow-900 text-yellow-500"
                  }`}
                >
                  {target?.isActive ? "Active" : "Paused"}
                </div>
              </div>
            </div>
          )}
          <div className="mt-[4px] flex justify-between">
            <div
              className="body-xs-medium-10 flex cursor-pointer items-center gap-[2px]"
              onClick={() => setIsShowDetails(!isShowDetails)}
            >
              {isShowDetails ? "Hide" : "More"} Detail{" "}
              <ChevronDownIcon
                className={`h-[12px] w-[12px] ${
                  isShowDetails ? "rotate-[180deg]" : ""
                }`}
              />
            </div>

            <div className="flex items-center gap-2.5">
              <button
                style={{
                  boxShadow:
                    "0px 0px 9px var(--0, 0px) var(--Brand-700, rgba(0, 204, 163, 0.60)), 0px 1px 2.3px 0px var(--Brand-300, #66FFE0) inset",
                }}
                className="bg-black-900 flex h-[30px] w-[30px]  items-center  justify-center gap-1 rounded-[6px] p-[4px]"
              >
                {target?.isActive ? (
                  <PauseIcon
                    className="cursor-pointer"
                    onClick={handlePauseTarget}
                  />
                ) : (
                  <StartIcon
                    className="cursor-pointer"
                    onClick={handlePauseTarget}
                  />
                )}
              </button>
              <div className="rounded-6 border-white-150 flex h-[30px] w-[30px] cursor-pointer items-center justify-center border p-[4px] text-[12px]">
                <RemoveSnipeIcon
                  className="cursor-pointer"
                  onClick={() => setIsOpenModalRemove(true)}
                />
                {isOpenModalRemove && (
                  <ModalRemoveSnipe
                    isOpen={isOpenModalRemove}
                    onClose={() => setIsOpenModalRemove(false)}
                    onConfirm={handleDeleteTarget}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }

    const _renderContent = () => {
      return (
        <div className="flex w-full flex-col gap-2">
          <div className="flex w-full">
            <div className="td text-white-500 flex w-[5%] justify-center">
              {index + 1}
            </div>

            {/* COPY WALLET */}
            <div className="td w-[13%]">
              <div className="flex items-center gap-1">
                <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                  {formatShortAddress(target.targetWalletAddress, 5, 3)}{" "}
                  <AppCopy message={target.targetWalletAddress} />
                </div>
              </div>
            </div>

            {/* WALLET NAME */}
            <div className="td w-[13%]">
              <div className="flex items-center gap-1">
                <div
                  title={target?.targetWalletName}
                  className="body-sm-regular-12 text-white-900 max-w-[100px] truncate"
                >
                  {target?.targetWalletName}
                </div>
              </div>
            </div>

            {/* COPY BUY */}
            <div className="td w-[16%] flex-col !items-start justify-center gap-0">
              {_renderCopyBuy()}
            </div>

            {/* COPY SELL */}
            <div className="td w-[16%] flex-col items-start justify-center gap-0">
              {_renderCopySell()}
            </div>

            {/* WALLET */}
            <div className="text-white-1000 td w-[12%] flex-col !items-start justify-center gap-0">
              <div className="flex items-center gap-2">
                <div className="body-sm-regular-12 text-white-900 flex items-center gap-1">
                  {formatShortAddress(target?.walletAddress, 5, 3)}{" "}
                  <AppCopy message={target?.walletAddress} />
                </div>
              </div>
            </div>

            {/* STATUS */}
            <div
              className={`td w-[10%] flex-col !items-start justify-center gap-0`}
            >
              <div className="flex items-center gap-2">
                <div
                  className={`rounded-4 flex items-center justify-center px-[4px] py-[1px] ${
                    target?.isActive
                      ? "bg-green-800 text-green-500"
                      : "bg-yellow-900 text-yellow-500"
                  }`}
                >
                  {target?.isActive ? "Active" : "Paused"}
                </div>
              </div>
            </div>

            {/* ACTION */}
            <div
              className={`td flex h-full w-[15%] min-w-[100px] items-center gap-2`}
            >
              <div className="flex items-center gap-2.5">
                <AutoSellDetail
                  autoSellSettings={target?.autoSellSettings}
                  onSetting={handleUpdateAutoSell}
                />

                {target?.isActive ? (
                  <PauseIcon
                    className="cursor-pointer"
                    onClick={handlePauseTarget}
                  />
                ) : (
                  <StartIcon
                    className="cursor-pointer"
                    onClick={handlePauseTarget}
                  />
                )}
                <RemoveSnipeIcon
                  className="cursor-pointer"
                  onClick={() => setIsOpenModalRemove(true)}
                />
                {isOpenModalRemove && (
                  <ModalRemoveSnipe
                    isOpen={isOpenModalRemove}
                    onClose={() => setIsOpenModalRemove(false)}
                    onConfirm={handleDeleteTarget}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      );
    };

    return <>{_renderContent()}</>;
  },
  (prevProps, nextProps) => {
    return (
      JSON.stringify(prevProps.target) === JSON.stringify(nextProps.target)
    );
  }
);

CopyTradeItem.displayName = "CopyTradeItem";

const CopyTradingList = ({ heightContent }: { heightContent: number }) => {
  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const handleWhenRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };
  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE,
      handleWhenRefreshData
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE,
        handleWhenRefreshData
      );
    };
  }, []);

  const getListTargets = async (dataTableParams?: any) => {
    if (dataTableParams?.page > 1) return { data: [] };
    const res = await rf
      .getRequest("CopyTradeRequest")
      .getListTargets(NETWORKS.SUI);

    return {
      data: res,
    };
  };

  if (!accessToken) {
    return (
      <div className="my-[100px] flex items-center justify-center">
        You need connect wallet to view this page
      </div>
    );
  }
  return (
    <div>
      {!isMobile && (
        <div
          className={`active-tab text-neutral-alpha-1000 border-neutral-alpha-50 flex h-[40px] w-full items-center gap-1 border-b px-4 py-3 text-[12px] font-semibold leading-[16px]`}
        >
          List Wallets Copying
        </div>
      )}

      <AppDataTable
        isHideHeader={isMobile}
        ref={dataTableRef}
        minWidth={900}
        height={heightContent}
        getData={getListTargets as any}
        renderHeader={() => (
          <>
            <div className="thead w-[5%] justify-center">No</div>
            <div className="thead w-[13%]">Copy Wallet</div>
            <div className="thead w-[13%]">Name</div>
            <div className="thead w-[16%]">Copy Buy</div>
            <div className="thead w-[16%]">Copy Sell </div>
            <div className="thead w-[12%]">Wallet</div>
            <div className="thead w-[12%]">Status</div>
            <div className="thead w-[13%] min-w-[60px]">
              Action
              <Tooltip
                overlay={`Pause or delete the copy trade, and auto sell orders won't be canceled.`}
                placement="top"
              >
                <InfoIcon className="h-[12px] w-[12px] cursor-pointer" />
              </Tooltip>
            </div>
          </>
        )}
        renderRow={(item: TCopyTradeTarget, index: number) => {
          return <CopyTradeItem target={item} index={index} />;
        }}
        overrideBodyClassName={
          isMobile ? "flex flex-col gap-2 mb-2 text-[12px]" : ""
        }
      />
    </div>
  );
};

export default CopyTradingList;
