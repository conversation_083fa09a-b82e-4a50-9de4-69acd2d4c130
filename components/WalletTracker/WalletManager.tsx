import React, { useEffect, useState } from "react";
import { EditIcon, TrashIcon, BellIcon } from "@/assets/icons";
import { AppButton, AppCopy } from "@/components";
import rf from "@/services/RequestFactory";
import ModalAddWallet from "@/modals/ModalAddWallet";
import { toastError, toastSuccess } from "@/libs/toast";
import { AppTimeDisplay } from "../AppTimeDisplay";
import { TWalletTracker } from "@/types/wallet-tracker";
import { ModalRemoveAllWallet } from "@/modals";
import { TGroup } from "@/types/wallet-tracker";
import Link from "next/link";
import { getLinkAddressExplorer } from "@/utils/helper";
import { NETWORKS } from "@/utils/contants";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "@/store/index";
import { setIsShowModalWalletTracker } from "@/store/metadata.store";
import Tooltip from "rc-tooltip";
import { RootState } from "@/store/index";
import ModalEditEmojiWallet from "../../modals/ModalEditEmojiWallet";

const WalletItem = ({
  wallet,
  onFetchData,
}: {
  wallet: TWalletTracker;
  onFetchData: () => void;
}) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [isShowEditEmoji, setIsShowEditEmoji] = useState<boolean>(false);
  const [name, setName] = useState<string>("");
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    setName(wallet.walletName);
  }, [wallet.walletName]);

  const deleteWallet = async () => {
    try {
      await rf.getRequest("WalletTrackerRequest").deleteWallet(wallet.id);
      toastSuccess("Success", "Deleted Successfully!");
      onFetchData();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.log(e);
    }
  };

  const onToggleSoundNotificationWallet = async () => {
    try {
      await rf.getRequest("WalletTrackerRequest").editWallet(wallet.id, {
        soundNotification: !wallet.soundNotification,
      });
      toastSuccess("Success", "Edit Successfully!");
      onFetchData();
      setIsEdit(false);
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.log(e);
    }
  };

  const handleKeyDown = async (e: any) => {
    if (e.key === "Enter") {
      try {
        await rf
          .getRequest("WalletTrackerRequest")
          .editWallet(wallet.id, { walletName: name });
        toastSuccess("Success", "Edit Successfully!");
        onFetchData();
        setIsEdit(false);
      } catch (e: any) {
        toastError("Error", e?.message || "Something went wrong!");
        console.log(e);
      }
    }
  };

  const encodedUrl = `/copy-trading?walletName=${encodeURIComponent(
    wallet.walletName
  )}&walletAddress=${encodeURIComponent(wallet.walletAddress)}`;

  return (
    <div className="mb-1 flex h-[40px] items-center justify-between md:mb-0">
      <div className="text-white-500 body-sm-regular-12 w-[62px] px-2 py-[10px] text-center underline">
        <Tooltip overlay={`Open in Suivision`} placement="right">
          <Link
            href={getLinkAddressExplorer(NETWORKS.SUI, wallet.walletAddress)}
            target="_blank"
          >
            <AppTimeDisplay timestamp={wallet.createdAt} isAgo suffix="" />
          </Link>
        </Tooltip>
      </div>
      <div className="body-sm-regular-12 flex w-[129px] max-w-[260px] flex-col gap-1 px-2 py-[10px] md:w-full md:flex-row md:items-center md:gap-0 md:gap-4">
        <div className="flex items-center gap-2">
          <div
            className="body-md-regular-14 cursor-pointer"
            onClick={() => setIsShowEditEmoji(true)}
          >
            {wallet?.emoji}
          </div>
          <div>
            {isEdit ? (
              <div className="flex items-center gap-2">
                <input
                  value={name}
                  onKeyDown={handleKeyDown}
                  onChange={(e) => setName(e.target.value)}
                  className="placeholder:text-white-300 border-white-500 body-sm-medium-12 w-[100px] truncate rounded-[2px] border-b bg-transparent outline-none"
                />
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="max-w-[100px] truncate">
                  {wallet.walletName || "--"}
                </div>
                <EditIcon
                  onClick={() => setIsEdit(true)}
                  className="cursor-pointer"
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-white-500 max-w-[80px] truncate">
            {wallet.walletAddress}
          </div>
          <AppCopy
            message={wallet.walletAddress}
            className="text-white-500 cursor-pointer"
          />
        </div>
      </div>

      <div className="flex w-[184px] max-w-[260px] items-center justify-end gap-[14px] px-2 py-[10px] md:w-full">
        <Tooltip
          overlay={
            wallet.soundNotification ? "Turn off sound" : "Turn on sound"
          }
          placement="top"
        >
          <BellIcon
            onClick={onToggleSoundNotificationWallet}
            className={`cursor-pointer ${
              wallet.soundNotification ? "text-brand-500" : "text-white-500"
            }`}
          />
        </Tooltip>
        {/*<ScanIcon className="cursor-pointer" />*/}
        <TrashIcon onClick={deleteWallet} className="cursor-pointer" />
        <Link
          href={encodedUrl}
          onClick={() =>
            dispatch(setIsShowModalWalletTracker({ isShow: false }))
          }
        >
          <AppButton
            variant="secondary"
            className="hidden h-[24px] !rounded !px-2 !py-[5px] !text-[10px] !font-medium !leading-[14px] md:block"
          >
            Copy trade
          </AppButton>
        </Link>
      </div>
      {isShowEditEmoji && (
        <ModalEditEmojiWallet
          wallet={wallet}
          isOpen={isShowEditEmoji}
          onClose={() => setIsShowEditEmoji(false)}
          onFetchData={onFetchData}
        />
      )}
    </div>
  );
};

const WalletManager = ({
  searchWallet,
  group,
  fetchGroups,
  getWallets,
  size,
  isPage,
}: {
  isPage?: boolean;
  searchWallet: string;
  size: any;
  group?: TGroup;
  fetchGroups: () => void;
  getWallets: () => void;
}) => {
  const [isAddWalletOpen, setAddWalletOpen] = useState<boolean>(false);
  const [isRemoveAllWallet, setIsRemoveAllWallet] = useState<boolean>(false);
  const [walletsShow, setWalletsShow] = useState<TWalletTracker[]>([]);
  const { wallets } = useSelector(
    (state: RootState) => state.user.walletTracker
  );

  useEffect(() => {
    let dataWallets = wallets;
    if (!!searchWallet) {
      dataWallets = dataWallets.filter(
        (item) =>
          item.walletAddress.includes(searchWallet) ||
          item.walletName.includes(searchWallet)
      );
    }
    setWalletsShow(dataWallets);
  }, [searchWallet, wallets]);

  return (
    <>
      <div className="text-white-500 border-white-50 flex h-[36px] items-center justify-between border-b">
        <div className="w-[62px] px-2 py-[6px] text-center text-[12px] font-normal leading-[18px]">
          Created
        </div>
        <div className="w-[129px] max-w-[260px] px-2 py-[6px] text-[12px] font-normal leading-[18px] md:w-full">
          Name
        </div>
        <div className="flex w-[184px] max-w-[260px] items-center md:w-full">
          <div className="flex flex-1 justify-end px-2 py-[6px] text-[12px] font-normal leading-[18px]">
            {wallets?.length}/500 wallets
          </div>
          <div
            onClick={() => setIsRemoveAllWallet(true)}
            className="text-brand-500 flex-1 cursor-pointer px-2 py-[10px] text-center text-[12px] font-medium leading-[16px]"
          >
            Remove All
          </div>
        </div>
      </div>

      <div
        className="customer-scroll flex-1 overflow-auto"
        style={{ height: isPage ? size.height - 90 : size.height - 125 }}
      >
        {!!walletsShow.length ? (
          walletsShow.map((wallet, index) => (
            <WalletItem wallet={wallet} key={index} onFetchData={getWallets} />
          ))
        ) : (
          <div className="body-md-regular-14 flex h-full items-center justify-center">
            No wallets added yet.
          </div>
        )}
      </div>

      {!isPage && (
        <div className="border-white-100 flex h-[40px] items-center justify-end border-t px-2">
          {/*<div className="flex items-center gap-[12px]">*/}
          {/*  <AppButton*/}
          {/*    variant="secondary"*/}
          {/*    className="h-[24px] !rounded text-[10px] font-medium leading-[14px]"*/}
          {/*  >*/}
          {/*    Import*/}
          {/*  </AppButton>*/}
          {/*  <AppButton*/}
          {/*    variant="secondary"*/}
          {/*    className="h-[24px] !rounded text-[10px] font-medium leading-[14px]"*/}
          {/*  >*/}
          {/*    Export*/}
          {/*  </AppButton>*/}
          {/*</div>*/}
          <AppButton
            variant="buy"
            className="h-[24px] !rounded text-[10px] font-medium leading-[14px]"
            onClick={() => setAddWalletOpen(true)}
          >
            Add Wallet
          </AppButton>
        </div>
      )}

      {isAddWalletOpen && (
        <ModalAddWallet
          isOpen={isAddWalletOpen}
          group={group}
          onClose={() => setAddWalletOpen(false)}
          fetchGroups={fetchGroups}
          onFetchData={getWallets}
        />
      )}

      {isRemoveAllWallet && (
        <ModalRemoveAllWallet
          onClose={() => setIsRemoveAllWallet(false)}
          onFetchData={getWallets}
          group={group}
          isOpen={isRemoveAllWallet}
        />
      )}
    </>
  );
};

export default WalletManager;
