import { FlashIcon } from "@/assets/icons";
import * as React from "react";
import { AppButton } from "@/components";
import clsx from "clsx";
import { ModalDeposits } from "@/modals/ModalDeposits";
import { useRaidenxWallet } from "@/hooks";
import { TPair } from "@/types";
import { isBuyBySuiToken, isPairWithSui } from "@/utils/pair";

export const ButtonAddFund = ({
  text = "Add Fund",
  className = "",
  pair,
  addressTokenQuoteSelected,
  symbolTokenQuoteSelected,
}: {
  text?: string;
  className?: string;
  pair?: TPair;
  addressTokenQuoteSelected?: string;
  symbolTokenQuoteSelected?: string;
}) => {
  const [isOpenDepositModal, setIsOpenDepositModal] =
    React.useState<boolean>(false);
  const { activeWallets } = useRaidenxWallet();

  const handleClick = () => {
    if (
      !pair ||
      isPairWithSui(pair) ||
      isBuyBySuiToken(addressTokenQuoteSelected || "")
    ) {
      return setIsOpenDepositModal(true);
    }

    return window.open(
      `/${pair.network}/token/${pair?.tokenQuote?.address}`,
      "_blank"
    );
  };

  const renderTextDisplay = () => {
    if (
      !pair ||
      isPairWithSui(pair) ||
      isBuyBySuiToken(addressTokenQuoteSelected || "")
    ) {
      return text;
    }
    return `Go Buy ${symbolTokenQuoteSelected}`;
  };

  return (
    <>
      <AppButton
        size="large"
        onClick={handleClick}
        className={clsx("flex justify-center gap-1", className)}
      >
        <FlashIcon />
        {renderTextDisplay()}
      </AppButton>

      {isOpenDepositModal && (
        <ModalDeposits
          isOpen={isOpenDepositModal}
          wallets={activeWallets}
          onClose={() => setIsOpenDepositModal(false)}
        />
      )}
    </>
  );
};
