import { NumericFormat } from "react-number-format";
import { AppLogoNetwork, AppSlider } from "@/components";
import BigNumber from "bignumber.js";
import { dividedBN, isZero } from "@/utils/helper";
import * as React from "react";
import { formatNumberWithCommas } from "@/utils/format";
import { ChevronDownIcon } from "@/assets/icons";
import { useEffect, useRef, useState } from "react";
import { useMediaQuery } from "react-responsive";
import Storage from "@/libs/storage";
import cx from "classnames";
import { NETWORKS } from "@/utils/contants";
import { SUI_DECIMALS } from "@/utils/contants";

export const SelectTokenQuote: React.FC<{
  options: {
    icon: any;
    symbol: string;
    address: string;
  }[];
  onSelect: (value: string) => void;
  value?: string;
}> = ({ options, onSelect, value }) => {
  const [selected, setSelected] = useState<any>(options[0]);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);
  const contentRef = useRef<any>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;
    const optionSelected = options.find((item) => item.address === value);
    setSelected(optionSelected);
  }, [value, options, isClient]);

  const handleClickOutside = (event: Event) => {
    if (
      contentRef.current &&
      !contentRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    if (!isClient) return;
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, [isClient]);

  if (!isClient) return null;

  return (
    <div
      className="relative"
      ref={contentRef}
      onClick={() => setShowDropdown(true)}
    >
      <div
        className={cx(
          "flex flex-row items-center justify-between",
          "gap-0.5",
          "w-max cursor-pointer"
        )}
      >
        <div>{selected?.icon}</div>
        <ChevronDownIcon
          className={`duration-500 ${
            showDropdown ? "rotate-[-180deg]" : "rotate-0"
          }`}
        />
      </div>

      {showDropdown && (
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.10) 100%), #08090C",
          }}
          className={cx(
            "absolute right-0 z-[9999] mt-1 w-full min-w-max rounded-[4px]"
          )}
        >
          <div className="flex w-full flex-col gap-1 p-1">
            {options.map((option, index) => {
              const isActive = option.address === value;
              return (
                <div
                  key={index}
                  className={cx(
                    "body-sm-regular-12 flex items-center gap-1 p-1",
                    "cursor-pointer",
                    "rounded-[4px]",
                    "tablet:hover:bg-neutral-alpha-50 text-neutral-0",
                    isActive ? "text-white-1000 bg-neutral-alpha-50" : ""
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelected(option);
                    onSelect(option.address);
                    setShowDropdown(false);
                  }}
                >
                  {option.icon} {option.symbol}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export interface OrderAmountConfig {
  rangeSliderDisabled?: boolean;
  steps: {
    type: "fixed" | "percent";
    values: number[];
  };
  multiCurrencyEnabled?: boolean;
  currencies?: {
    symbol: string;
    address: string;
    decimals: number;
    icon: any;
    isDefault?: boolean;
  }[];
}

export const AmountForm = ({
  value,
  onChange,
  percent,
  balance,
  currencySelected,
  setCurrencySelected,
  configs,
}: {
  currencySelected?: string;
  setCurrencySelected?: (value: string) => void;
  value: any;
  onChange: (value: any) => void;
  percent?: any;
  balance?: any;
  configs: OrderAmountConfig;
}) => {
  const [isClient, setIsClient] = useState(false);
  const amountRef = useRef<any>("0");
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const { rangeSliderDisabled, steps, multiCurrencyEnabled } = configs;

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFocusInputAmount = () => {
    if (!isClient) return;
    const formattedAmount = formatNumberWithCommas(value);
    amountRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };

  if (!isClient) return null;

  const getPrefix = () => {
    if (steps.type === "percent") {
      return <div className="text-neutral-alpha-500 body-xs-regular-10">%</div>;
    }

    if (multiCurrencyEnabled && !!configs?.currencies?.length) {
      return (
        <SelectTokenQuote
          options={configs?.currencies}
          onSelect={(value) => {
            if (setCurrencySelected) {
              setCurrencySelected(value);
            }
            Storage.setUserSettings("tokenQuoteSelected", value);
          }}
          value={currencySelected}
        />
      );
    }

    return (
      <div className="text-neutral-alpha-500">
        <AppLogoNetwork
          network={NETWORKS.SUI}
          isBase
          className="h-[14px] w-[14px]"
        />
      </div>
    );
  };

  const getDecimals = () => {
    if (steps.type === "percent") {
      return 2;
    }

    if (!multiCurrencyEnabled) {
      return SUI_DECIMALS;
    }

    return configs?.currencies?.find((c: any) => c.symbol === currencySelected)
      ?.decimals;
  };

  return (
    <>
      <div className="tablet:border border-neutral-alpha-50 tablet:rounded-[8px] mt-3">
        <div className="bg-neutral-beta-800 tablet:rounded-t-[8px] tablet:border-0 border-white-50 flex items-center gap-2 rounded-[4px] border p-2 ">
          <div className="body-sm-regular-12 text-neutral-alpha-800">
            Amount
          </div>
          <NumericFormat
            getInputRef={amountRef}
            defaultValue={value ?? ""}
            value={value ?? ""}
            autoFocus={!isMobile}
            onFocus={handleFocusInputAmount}
            allowLeadingZeros
            allowNegative={false}
            thousandSeparator=","
            className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
            decimalScale={getDecimals()}
            onValueChange={({ floatValue }) => {
              if (!isZero(floatValue)) {
                onChange(floatValue?.toString());
              } else {
                onChange("");
              }
            }}
          />

          {getPrefix()}
        </div>

        <div className="tablet:gap-0 tablet:mt-0 mt-[4px] grid h-[34px] grid-cols-4 gap-1">
          {steps.values?.map((item, index) => {
            return (
              <div
                onClick={() => onChange(item)}
                key={index}
                className={`g tablet:rounded-[0px] tablet:border-0 border-neutral-alpha-100 hover:bg-neutral-alpha-50 flex cursor-pointer items-center justify-center rounded-[4px] border py-[4px] text-center ${
                  index + 1 === 4
                    ? ""
                    : "tablet:border-r border-neutral-alpha-50"
                } ${+value === item ? "bg-neutral-alpha-50" : ""}
                 ${
                   steps.type === "percent"
                     ? "body-sm-regular-12"
                     : "body-md-regular-14"
                 }
                `}
              >
                {item} {steps.type === "percent" ? "%" : ""}
              </div>
            );
          })}
        </div>
      </div>

      {rangeSliderDisabled && (
        <div className="mt-4 flex items-end gap-4">
          <AppSlider
            value={percent}
            onChange={(value: number | string) => {
              onChange(
                new BigNumber(dividedBN(Number(value), 100))
                  .multipliedBy(balance)
                  .toString()
              );
            }}
            disabled={!balance}
          />
        </div>
      )}
    </>
  );
};
