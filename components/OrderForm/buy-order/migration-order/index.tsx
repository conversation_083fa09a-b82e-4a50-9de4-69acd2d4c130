import BigNumber from "bignumber.js";
import * as React from "react";
import { useContext, useState } from "react";
import { AppDropdown, AppNumber } from "@/components";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { TPair, TPairPrice } from "@/types";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { useEffect } from "react";
import { isPairWithSui } from "@/utils/pair";
import { useMediaQuery } from "react-responsive";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import Storage from "@/libs/storage";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { SwapSettingsBar } from "../SwapSettingsBar";
import { dividedBN, sleep } from "@/utils/helper";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { ModalAutoSell } from "../AutoSellForm";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { useOrder } from "@/hooks/useOrder";
import { toastError } from "@/libs/toast";
import { useAggregatorAutoToggle } from "@/hooks/useAggregatorAutoToggle";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { useMemo } from "react";
import { EDex } from "@/enums/dex.enum";
import { convertMistToDec, minusBN } from "@/utils/helper";

export const OrderFormBuyMigration = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  addressTokenQuoteSelected,
  symbolTokenQuoteSelected,
  setAddressTokenQuoteSelected,
  activeTotalQuoteBalance,
  setOrderType,
  orderType,
  listCurrency,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  totalBalanceTokenBase: string | number;
  addressTokenQuoteSelected: string;
  activeTotalQuoteBalance: string | number;
  symbolTokenQuoteSelected: string;
  setAddressTokenQuoteSelected: (value: string) => void;
  setOrderType: (value: string) => void;
  orderType: any;
  listCurrency: any[];
}) => {
  const [amount, setAmount] = useState<any>("");
  const orderSettings = Storage.getOrderSettings();
  const [percent, setPercent] = useState<any>("0");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    orderSettings?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(
    orderSettings?.autoSell?.receiveToken || null
  );
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    orderSettings?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { buyMigration } = useOrder();

  const { pair, poolObjects } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
    poolObjects: any;
  };

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  // get amount can buy with Moonbags Dex
  const amountCanBuyWithMoonBagsDex = useMemo(() => {
    if (
      !!poolObjects &&
      pair?.dex?.dex === EDex.MOONBAGS &&
      Number(pair?.bondingCurve || 0) < 1
    ) {
      return convertMistToDec(
        minusBN(
          poolObjects?.threshold,
          poolObjects?.real_sui_reserves?.fields?.balance
        )
      );
    }

    return amount;
  }, [poolObjects, amount, pair?.bondingCurve, pair?.dex?.dex]);

  const createOrder = async () => {
    if (new BigNumber(amount).isZero() || isLoading) return;

    let autoSellSettings = null as any;

    if (isSettingAutoSell) {
      if (!triggersOrder?.length) {
        toastError("Error", "Please setting for auto sell");
        return;
      }

      autoSellSettings = {
        receiveToken: isPairWithSui(pair) ? null : receiveToken,
        triggers: triggersOrder,
      };
    }

    const maxAmountCanBuy = BigNumber.min(
      amount,
      amountCanBuyWithMoonBagsDex
    ).toString();

    setIsLoading(true);
    await buyMigration(
      autoSellSettings,
      pair,
      maxAmountCanBuy,
      addressTokenQuoteSelected,
      useAggregator,
      async () => {
        setAmount("");
        setIsLoading(false);
        await sleep(2000);
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
      }
    ).then();
    setIsLoading(false);
  };

  useEffect(() => {
    if (!amount) {
      setPercent("0");
      return;
    }

    if (!!+amount) {
      const newPercent = new BigNumber(
        dividedBN(amount, activeTotalQuoteBalance)
      )
        .multipliedBy(100)
        .integerValue(BigNumber.ROUND_FLOOR)
        .toString();
      setPercent(BigNumber.min(newPercent, 100).toString());
    }
  }, [amount]);

  useEffect(() => {
    setAmount("");
  }, [addressTokenQuoteSelected]);

  useEffect(() => {
    if (!accessToken) {
      setIsSettingAutoSell(false);
    }
  }, [accessToken]);

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setOrderSettings({
        ...orderSettings,
        autoSell: {
          ...orderSettings.autoSell,
          isSettingAutoSell: false,
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setOrderSettings({
      ...orderSettings,
      autoSell: {
        ...orderSettings.autoSell,
        isSettingAutoSell: true,
      },
    });
  };

  const onOpenSettingAutoSell = () => {
    if (!accessToken) return;
    setIsShowSettingAutoSell(true);
  };

  const _renderTextBtn = () => {
    return (
      <>
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12 flex items-center gap-1">
          Buy {!!+amount && <AppNumber value={amount} />}
          {symbolTokenQuoteSelected}
        </div>
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        type={TRADE_TYPE.BUY}
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
        symbolTokenQuoteSelected={symbolTokenQuoteSelected}
        activeTotalQuoteBalance={activeTotalQuoteBalance}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[90px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={amount}
        onChange={setAmount}
        percent={percent}
        balance={activeTotalQuoteBalance}
        setCurrencySelected={setAddressTokenQuoteSelected}
        currencySelected={addressTokenQuoteSelected}
        configs={{
          rangeSliderDisabled: true,
          steps: {
            type: "fixed",
            values: orderSettings?.defaultBuyAmount,
          },
          multiCurrencyEnabled: listCurrency.length > 1,
          currencies: listCurrency,
        }}
      />

      <>
        <SwapSettingsBar
          onShowSettings={onShowSettings}
          orderType={orderType}
          amount={amount}
          onShowSettingAutoSell={onOpenSettingAutoSell}
          autoSell={isSettingAutoSell}
          toggleSetAutoSell={toggleSettingAutoSell}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />

        <ButtonOrderSubmit
          isLoading={isLoading}
          amount={amount}
          pair={pair}
          text={_renderTextBtn()}
          onShowSettings={onShowSettings}
          createOrder={createOrder}
          symbolTokenQuoteSelected={symbolTokenQuoteSelected}
          addressTokenQuoteSelected={addressTokenQuoteSelected}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
      </>

      <ModalAutoSell
        pair={pair}
        onClose={() => setIsShowSettingAutoSell(false)}
        isOpen={isShowSettingAutoSell}
        setReceiveToken={setReceiveToken}
        receiveToken={receiveToken}
        setTriggersOrder={setTriggersOrder}
        triggersOrder={triggersOrder}
        isOrder
      />
    </div>
  );
};
