import BigNumber from "bignumber.js";
import * as React from "react";
import { useContext, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { AppDropdown } from "@/components";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { RootState } from "@/store";
import { TPair, TPairPrice } from "@/types";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { EDex } from "@/enums";
import { convertMistToDec, minusBN, sleep } from "@/utils/helper";
import Storage from "@/libs/storage";
import { useEffect } from "react";
import { isPairWithSui } from "@/utils/pair";
import { useMediaQuery } from "react-responsive";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { ModalAutoSell } from "../AutoSellForm";
import { toastError } from "@/libs/toast";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { dividedBN } from "@/utils/helper";
import { SwapSettingsBar } from "../SwapSettingsBar";
import { useRef } from "react";
import { useCallback } from "react";
import { debounce } from "lodash";
import { useRaidenxWallet } from "@/hooks/useRaidenxWallet";
import { multipliedBN } from "@/utils/helper";
import { formatNumber } from "@/utils/format";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { AppNumber } from "../../../AppNumber";

export const OrderFormBuyMarket = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  addressTokenQuoteSelected,
  symbolTokenQuoteSelected,
  setAddressTokenQuoteSelected,
  activeTotalQuoteBalance,
  setOrderType,
  orderType,
  listCurrency,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  totalBalanceTokenBase: string | number;
  addressTokenQuoteSelected: string;
  symbolTokenQuoteSelected: string;
  activeTotalQuoteBalance: string | number;
  setAddressTokenQuoteSelected: (value: string) => void;
  setOrderType: (value: string) => void;
  orderType: any;
  listCurrency: any[];
}) => {
  const orderSettings = Storage.getOrderSettings();
  const [amount, setAmount] = useState<any>("");
  const [percent, setPercent] = useState<any>("0");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  //autoSell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    orderSettings?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(
    orderSettings?.autoSell?.receiveToken || null
  );
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    orderSettings?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  // estimate
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");
  const tokenAmountEstimateRef = useRef<any>("");
  const targetConvertPercent = useRef("1");
  const { estimateQuickBuy } = useOrder();
  const { activeWalletAddresses } = useRaidenxWallet();

  const { pair, poolObjects } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
    poolObjects: any;
  };

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { quickBuy } = useOrder();

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  // get amount can buy with Moonbags Dex
  const amountCanBuyWithMoonBagsDex = useMemo(() => {
    if (
      !!poolObjects &&
      pair?.dex?.dex === EDex.MOONBAGS &&
      Number(pair?.bondingCurve || 0) < 1
    ) {
      return convertMistToDec(
        minusBN(
          poolObjects?.threshold,
          poolObjects?.real_sui_reserves?.fields?.balance
        )
      );
    }

    return amount;
  }, [poolObjects, amount, pair?.bondingCurve, pair?.dex?.dex]);

  const debouncedEstimateQuickBuy = useCallback(
    debounce((pair, amount) => {
      estimateQuickBuy(pair, amount, addressTokenQuoteSelected).then(
        (totalAmountOut) => {
          if (totalAmountOut) {
            tokenAmountEstimateRef.current = multipliedBN(
              totalAmountOut,
              targetConvertPercent.current
            );
            setTokenAmountEstimate(tokenAmountEstimateRef.current);
          }
        }
      );
    }, 1500),
    [activeWalletAddresses?.length, addressTokenQuoteSelected]
  );

  useEffect(() => {
    if (!accessToken || !amount) return;
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    debouncedEstimateQuickBuy(pair, amount);

    return () => {
      debouncedEstimateQuickBuy.cancel();
    };
  }, [
    accessToken,
    amount,
    activeWalletAddresses?.length,
    addressTokenQuoteSelected,
  ]);

  const createOrder = async () => {
    if (new BigNumber(amount).isZero() || isLoading) return;

    let autoSellSettings = null as any;
    if (isSettingAutoSell) {
      if (!triggersOrder?.length) {
        toastError("Error", "Please setting for auto sell");
        return;
      }

      autoSellSettings = {
        receiveToken: isPairWithSui(pair) ? null : receiveToken,
        triggers: triggersOrder,
      };
    }

    const maxAmountCanBuy = BigNumber.min(
      amount,
      amountCanBuyWithMoonBagsDex
    ).toString();

    setIsLoading(true);
    await quickBuy(
      autoSellSettings,
      pair,
      maxAmountCanBuy,
      addressTokenQuoteSelected,
      useAggregator,
      async () => {
        setAmount("");
        setIsLoading(false);
        await sleep(2000);
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
      }
    ).then();
    setIsLoading(false);
  };

  useEffect(() => {
    setAmount("");
  }, [addressTokenQuoteSelected]);

  useEffect(() => {
    if (!accessToken) {
      setIsSettingAutoSell(false);
    }
  }, [accessToken]);

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setOrderSettings({
        ...orderSettings,
        autoSell: {
          ...orderSettings.autoSell,
          isSettingAutoSell: false,
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setOrderSettings({
      ...orderSettings,
      autoSell: {
        ...orderSettings.autoSell,
        isSettingAutoSell: true,
      },
    });
  };

  const onOpenSettingAutoSell = () => {
    if (!accessToken) return;
    setIsShowSettingAutoSell(true);
  };

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!amount) {
      setPercent("0");
      return;
    }

    if (!!+amount) {
      const newPercent = new BigNumber(
        dividedBN(amount, activeTotalQuoteBalance)
      )
        .multipliedBy(100)
        .integerValue(BigNumber.ROUND_FLOOR)
        .toString();
      setPercent(BigNumber.min(newPercent, 100).toString());
    }
  }, [accessToken, amount]);

  const _renderTextBtn = () => {
    return (
      <>
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12 flex items-center gap-1">
          Buy {!!+amount && <AppNumber value={amount} />}{" "}
          {symbolTokenQuoteSelected}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈{formatNumber(tokenAmountEstimate, pair?.tokenBase?.decimals)}{" "}
            {pair?.tokenBase?.symbol})
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
        symbolTokenQuoteSelected={symbolTokenQuoteSelected}
        activeTotalQuoteBalance={activeTotalQuoteBalance}
        type={TRADE_TYPE.BUY}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[90px] rounded-none border-r border-solid"
            value={orderType}
          />
          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={amount}
        onChange={setAmount}
        percent={percent}
        setCurrencySelected={setAddressTokenQuoteSelected}
        currencySelected={addressTokenQuoteSelected}
        configs={{
          rangeSliderDisabled: true,
          steps: {
            type: "fixed",
            values: orderSettings?.defaultBuyAmount,
          },
          multiCurrencyEnabled: listCurrency.length > 1,
          currencies: listCurrency,
        }}
        balance={activeTotalQuoteBalance}
      />

      <>
        <SwapSettingsBar
          onShowSettings={onShowSettings}
          orderType={orderType}
          amount={amount}
          onShowSettingAutoSell={onOpenSettingAutoSell}
          autoSell={isSettingAutoSell}
          toggleSetAutoSell={toggleSettingAutoSell}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
        <ButtonOrderSubmit
          isLoading={isLoading}
          amount={amount}
          pair={pair}
          text={_renderTextBtn()}
          onShowSettings={onShowSettings}
          createOrder={createOrder}
          symbolTokenQuoteSelected={symbolTokenQuoteSelected}
          addressTokenQuoteSelected={addressTokenQuoteSelected}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
      </>

      <ModalAutoSell
        pair={pair}
        onClose={() => setIsShowSettingAutoSell(false)}
        isOpen={isShowSettingAutoSell}
        setReceiveToken={setReceiveToken}
        receiveToken={receiveToken}
        setTriggersOrder={setTriggersOrder}
        triggersOrder={triggersOrder}
        isOrder
      />
    </div>
  );
};
