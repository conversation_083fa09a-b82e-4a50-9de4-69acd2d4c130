import BigNumber from "bignumber.js";
import * as React from "react";
import { useContext, useState } from "react";
import { useSelector } from "react-redux";
import { AppDropdown } from "@/components";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { RootState } from "@/store";
import { TPair, TPairPrice } from "@/types";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { OrderLimitTargetType } from "@/enums";
import { dividedBN, multipliedBN, toStringBN } from "@/utils/helper";
import Storage from "@/libs/storage";
import { useEffect } from "react";
import { debounce, isEmpty } from "lodash";
import { getCirculatingSupply, isPairWithSui } from "@/utils/pair";
import { useMediaQuery } from "react-responsive";
import { TargetForm } from "@/components/OrderForm/components/TargetForm";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { ModalAutoSell } from "../AutoSellForm";
import { toastError } from "@/libs/toast";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { SwapSettingsBar } from "../SwapSettingsBar";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { formatNumber } from "@/utils/format";
import { useRef } from "react";
import { useRaidenxWallet } from "@/hooks/useRaidenxWallet";
import { useCallback } from "react";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { AppNumber } from "../../../AppNumber";

export const OrderFormBuyLimit = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  addressTokenQuoteSelected,
  symbolTokenQuoteSelected,
  setAddressTokenQuoteSelected,
  activeTotalQuoteBalance,
  setOrderType,
  orderType,
  listCurrency,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  totalBalanceTokenBase: string | number;
  addressTokenQuoteSelected: string;
  symbolTokenQuoteSelected: string;
  activeTotalQuoteBalance: string | number;
  setAddressTokenQuoteSelected: (value: string) => void;
  setOrderType: (value: string) => void;
  orderType: any;
  listCurrency: any[];
}) => {
  const userSettings = Storage.getUserSettings();
  const orderSettings = Storage.getOrderSettings();

  const [amount, setAmount] = useState<any>("");
  const [percent, setPercent] = useState<any>("0");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // for Limit
  const [targetMC, setTargetMC] = useState<any>("");
  const [targetPrice, setTargetPrice] = useState<any>("");
  const [targetType, setTargetType] = useState<any>(
    userSettings.orderLimitTargetType || OrderLimitTargetType.MC
  );

  //autoSell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    orderSettings?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(
    orderSettings?.autoSell?.receiveToken || null
  );
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    orderSettings?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  const { pair, pairPrice } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
    poolObjects: any;
  };

  //estimate
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");
  const tokenAmountEstimateRef = useRef<any>("");
  const targetConvertPercent = useRef("1");
  const { estimateQuickBuy } = useOrder();
  const { activeWalletAddresses } = useRaidenxWallet();

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { buyLimit } = useOrder();
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const createOrder = async () => {
    if (new BigNumber(amount).isZero() || isLoading) return;

    let priceTarget = targetPrice;
    if (targetType === OrderLimitTargetType.MC) {
      priceTarget = dividedBN(targetMC, getCirculatingSupply(pair));
    }

    let autoSellSettings = null as any;
    if (isSettingAutoSell) {
      if (!triggersOrder?.length) {
        toastError("Error", "Please setting for auto sell");
        return;
      }

      autoSellSettings = {
        receiveToken: isPairWithSui(pair) ? null : receiveToken,
        triggers: triggersOrder,
      };
    }
    setIsLoading(true);
    await buyLimit(
      autoSellSettings,
      pair,
      amount,
      priceTarget,
      addressTokenQuoteSelected,
      useAggregator,
      () => {
        setAmount("");
        setIsLoading(false);
      }
    ).then();
    setIsLoading(false);
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    setTargetMC(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
    setTargetPrice(toStringBN(pairPrice?.priceUsd));
    Storage.setUserSettings("orderLimitTargetType", targetType);
  }, [pair?.pairId, targetType]);

  useEffect(() => {
    setAmount("");
  }, [addressTokenQuoteSelected]);

  useEffect(() => {
    if (!accessToken) {
      setIsSettingAutoSell(false);
    }
  }, [accessToken]);

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setOrderSettings({
        ...orderSettings,
        autoSell: {
          ...orderSettings.autoSell,
          isSettingAutoSell: false,
        },
      });
      return;
    }
    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setOrderSettings({
      ...orderSettings,
      autoSell: {
        ...orderSettings.autoSell,
        isSettingAutoSell: true,
      },
    });
  };

  const onOpenSettingAutoSell = () => {
    if (!accessToken) return;
    setIsShowSettingAutoSell(true);
  };

  const debouncedEstimateQuickBuy = useCallback(
    debounce((pair, amount) => {
      estimateQuickBuy(pair, amount, addressTokenQuoteSelected).then(
        (totalAmountOut) => {
          if (totalAmountOut) {
            tokenAmountEstimateRef.current = multipliedBN(
              totalAmountOut,
              targetConvertPercent.current
            );
            setTokenAmountEstimate(tokenAmountEstimateRef.current);
          }
        }
      );
    }, 1500),
    [activeWalletAddresses?.length, addressTokenQuoteSelected]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!accessToken || !amount) return;
    debouncedEstimateQuickBuy(pair, amount);

    return () => {
      debouncedEstimateQuickBuy.cancel();
    };
  }, [
    accessToken,
    amount,
    targetPrice,
    targetMC,
    activeWalletAddresses?.length,
    addressTokenQuoteSelected,
  ]);

  useEffect(() => {
    targetConvertPercent.current = "1";

    let rate;
    if (targetType === OrderLimitTargetType.MC) {
      rate = dividedBN(
        multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)),
        targetMC
      );
    } else if (targetType === OrderLimitTargetType.PRICE) {
      rate = dividedBN(pairPrice?.priceUsd, targetPrice);
    } else {
      return;
    }

    targetConvertPercent.current = rate;
    if (tokenAmountEstimateRef.current) {
      tokenAmountEstimateRef.current = multipliedBN(
        tokenAmountEstimateRef.current,
        rate
      );
      setTokenAmountEstimate(tokenAmountEstimateRef.current);
    }
  }, [accessToken, targetType, targetPrice, targetMC]);

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);

    if (!amount) {
      setPercent("0");
      return;
    }

    if (!!+amount) {
      const newPercent = new BigNumber(
        dividedBN(amount, activeTotalQuoteBalance)
      )
        .multipliedBy(100)
        .integerValue(BigNumber.ROUND_FLOOR)
        .toString();
      setPercent(BigNumber.min(newPercent, 100).toString());
    }
  }, [accessToken, amount]);

  const _renderTextBtn = () => {
    return (
      <>
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12 flex items-center gap-1">
          Buy {!!+amount && <AppNumber value={amount} />}{" "}
          {symbolTokenQuoteSelected}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈{formatNumber(tokenAmountEstimate, pair?.tokenBase?.decimals)}{" "}
            {pair?.tokenBase?.symbol})
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
        symbolTokenQuoteSelected={symbolTokenQuoteSelected}
        activeTotalQuoteBalance={activeTotalQuoteBalance}
        type={TRADE_TYPE.BUY}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[90px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="flex-1">
            <TargetForm
              targetType={targetType}
              setTargetType={setTargetType}
              targetMC={targetMC}
              setTargetMC={setTargetMC}
              targetPrice={targetPrice}
              setTargetPrice={setTargetPrice}
              pair={pair}
            />
          </div>
        </div>
      )}

      <AmountForm
        value={amount}
        onChange={setAmount}
        percent={percent}
        setCurrencySelected={setAddressTokenQuoteSelected}
        currencySelected={addressTokenQuoteSelected}
        configs={{
          rangeSliderDisabled: true,
          steps: {
            type: "fixed",
            values: orderSettings?.defaultBuyAmount,
          },
          multiCurrencyEnabled: listCurrency.length > 1,
          currencies: listCurrency,
        }}
        balance={activeTotalQuoteBalance}
      />

      {!isMobile && (
        <div className="border-neutral-alpha-50 mt-6 overflow-hidden rounded-[8px] border">
          <TargetForm
            targetType={targetType}
            setTargetType={setTargetType}
            targetMC={targetMC}
            setTargetMC={setTargetMC}
            targetPrice={targetPrice}
            setTargetPrice={setTargetPrice}
            pair={pair}
          />
        </div>
      )}

      <>
        <SwapSettingsBar
          onShowSettings={onShowSettings}
          orderType={orderType}
          amount={amount}
          onShowSettingAutoSell={onOpenSettingAutoSell}
          autoSell={isSettingAutoSell}
          toggleSetAutoSell={toggleSettingAutoSell}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
        <ButtonOrderSubmit
          isLoading={isLoading}
          amount={amount}
          pair={pair}
          text={_renderTextBtn()}
          onShowSettings={onShowSettings}
          createOrder={createOrder}
          symbolTokenQuoteSelected={symbolTokenQuoteSelected}
          addressTokenQuoteSelected={addressTokenQuoteSelected}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
      </>

      <ModalAutoSell
        pair={pair}
        onClose={() => setIsShowSettingAutoSell(false)}
        isOpen={isShowSettingAutoSell}
        setReceiveToken={setReceiveToken}
        receiveToken={receiveToken}
        setTriggersOrder={setTriggersOrder}
        triggersOrder={triggersOrder}
        isOrder
      />
    </div>
  );
};
