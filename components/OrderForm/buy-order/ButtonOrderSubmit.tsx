import { SettingsIcon } from "@/assets/icons";
import * as React from "react";
import BigNumber from "bignumber.js";
import { ButtonSignIn } from "@/components/OrderForm/ButtonSignin";
import { ButtonAddWallet } from "@/components/OrderForm/ButtonAddWallet";
import { ButtonAddFund } from "@/components/OrderForm/ButtonAddFund";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";
import { AppButton } from "@/components";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useMediaQuery } from "react-responsive";
import { TPair } from "@/types";
import { isPairWithSui } from "@/utils/pair";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { ClipLoader } from "react-spinners";

export const ButtonOrderSubmit = ({
  amount,
  pair,
  onShowSettings,
  createOrder,
  symbolTokenQuoteSelected,
  addressTokenQuoteSelected,
  text,
  activeTotalQuoteBalance,
  isLoading,
}: {
  isLoading: boolean;
  amount: any;
  pair: TPair;
  text: any;
  onShowSettings: () => void;
  createOrder: () => void;
  symbolTokenQuoteSelected: string;
  addressTokenQuoteSelected: string;
  activeTotalQuoteBalance: any;
}) => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const isExceedBalance = () => {
    return new BigNumber(amount).isGreaterThan(activeTotalQuoteBalance);
  };

  const _renderButton = () => {
    if (!accessToken) {
      return (
        <ButtonSignIn type={TRADE_TYPE.BUY} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!wallets.length) {
      return (
        <ButtonAddWallet type={TRADE_TYPE.BUY} className="tablet:mt-2 !mt-0" />
      );
    }

    if (+amount > +activeTotalQuoteBalance || !+activeTotalQuoteBalance) {
      return (
        <>
          <ButtonAddFund
            className="tablet:mt-2 !mt-0"
            pair={pair}
            symbolTokenQuoteSelected={symbolTokenQuoteSelected}
            addressTokenQuoteSelected={addressTokenQuoteSelected}
          />

          {!isPairWithSui(pair) && !amount && (
            <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
              {`You need ${symbolTokenQuoteSelected} to buy ${pair?.tokenBase?.symbol}`}
            </div>
          )}
        </>
      );
    }

    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    return (
      <div
        className={`tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] flex-col items-center justify-center rounded-[8px] border px-2 ${
          isLoading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
        }`}
        onClick={createOrder}
      >
        {isLoading ? <ClipLoader color="#8d93b7" size={15} /> : text}
      </div>
    );
  };

  if (isMobile) {
    return (
      <>
        <div className="mt-3">
          <div className="flex w-full items-center gap-2">
            <div className="flex-1">{_renderButton()}</div>
            <div
              onClick={onShowSettings}
              className="border-neutral-alpha-500 tablet:p-0 text-neutral-alpha-500 body-sm-medium-12 hover:text-neutral-alpha-1000 flex h-max cursor-pointer gap-1 rounded-md border border-solid p-2"
            >
              <SettingsIcon className="h-5 w-5" />
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div>{_renderButton()}</div>

      {isExceedBalance() && (
        <div className="body-xs-regular-10 tablet:block mt-2 hidden text-center text-yellow-500">
          Exceeds wallet balance
        </div>
      )}
    </>
  );
};
