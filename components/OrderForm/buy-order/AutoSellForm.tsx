import clsx from "clsx";
import AppDrawer from "@/components/AppDrawer";
import React, { useEffect, useState } from "react";
import {
  CheckboxIcon,
  CheckedIcon,
  CloseIcon,
  PlusIcon,
  TrashIcon,
} from "@/assets/icons";
import { AppButton } from "@/components";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { NumericFormat } from "react-number-format";
import { toastError } from "@/libs/toast";
import { isPairWithSui } from "@/utils/pair";
import { TPair } from "@/types";
import BigNumber from "bignumber.js";
import Storage from "@/libs/storage";

interface ITrigger {
  priceChangePercent: any;
  sellPercent: any;
}

export const ModalAutoSell = ({
  isOpen,
  receiveToken,
  setReceiveToken,
  onClose,
  triggersOrder,
  setTriggersOrder,
  isOrder,
  isShowReceiveSui,
  typeSnipe,
  pair,
}: {
  isOpen: boolean;
  setReceiveToken?: (value: string | null) => void;
  triggersOrder: any;
  setTriggersOrder: (value: any) => void;
  receiveToken?: string | null;
  onClose: () => void;
  isOrder?: boolean;
  isShowReceiveSui?: boolean;
  typeSnipe?: string;
  pair?: TPair;
}) => {
  return (
    <AppDrawer
      isOpen={isOpen}
      className={clsx(
        "border-white-100 bottom-0 top-auto !h-[calc(100%-52px)] !w-[325px] border-l bg-[#141518]"
      )}
      enableOverlay={false}
    >
      <div>
        <div className="border-white-100 flex items-center justify-between border-b px-2 py-4">
          <div className="text-brand-500 body-md-regular-14">Auto Sell</div>
          <CloseIcon className="cursor-pointer" onClick={onClose} />
        </div>

        {isOpen && (
          <AutoSellForm
            pair={pair}
            isOrder={isOrder}
            isShowReceiveSui={isShowReceiveSui}
            onClose={onClose}
            setReceiveToken={setReceiveToken}
            receiveToken={receiveToken}
            setTriggersOrder={setTriggersOrder}
            triggersOrder={triggersOrder}
            typeSnipe={typeSnipe}
          />
        )}
      </div>
    </AppDrawer>
  );
};

const AutoSellForm = ({
  receiveToken,
  setReceiveToken,
  onClose,
  triggersOrder,
  setTriggersOrder,
  isOrder,
  isShowReceiveSui,
  typeSnipe,
  pair,
}: {
  setReceiveToken?: (value: string | null) => void;
  triggersOrder: any;
  setTriggersOrder: (value: any) => void;
  receiveToken?: string | null;
  onClose: () => void;
  isOrder?: boolean;
  isShowReceiveSui?: boolean;
  typeSnipe?: string;
  pair?: TPair;
}) => {
  const orderSettings = Storage.getOrderSettings();
  const snipeSettings = Storage.getSnipeSettings();

  const [triggers, setTriggers] = useState<ITrigger[]>([
    {
      priceChangePercent: "",
      sellPercent: "",
    },
  ]);
  const [isReceiveSui, setIsReceiveSui] = useState<boolean>(false);

  useEffect(() => {
    if (!!receiveToken) {
      setIsReceiveSui(true);
    }
  }, [receiveToken]);

  useEffect(() => {
    if (!!triggersOrder.length) {
      setTriggers(triggersOrder);
    }
  }, [triggersOrder]);

  const addTrigger = () => {
    if (triggers.length === 5) return;
    setTriggers((prev) =>
      prev.concat([
        {
          priceChangePercent: "",
          sellPercent: "",
        },
      ])
    );
  };

  const removeTrigger = (indexToDelete: number) => {
    setTriggers(
      triggers.filter((_: ITrigger, index: number) => index !== indexToDelete)
    );
  };

  const onSave = () => {
    if (!triggers.length) {
      toastError("Error", "Please add order!");
      return;
    }

    if (
      triggers.some(
        (trigger) => !trigger.sellPercent || !trigger.priceChangePercent
      )
    ) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (
      triggers.some((trigger) =>
        new BigNumber(trigger.priceChangePercent).lt(-100)
      )
    ) {
      toastError(
        "Error",
        "Please provide a valid trigger percentage (greater than -100%)."
      );
      return;
    }

    if (
      triggers.some(
        (trigger) =>
          new BigNumber(trigger.sellPercent).gt(100) ||
          new BigNumber(trigger.sellPercent).lt(0.1)
      )
    ) {
      toastError("Error", "Please provide a valid sell percentage.");
      return;
    }

    const dataFormat = triggers.map((trigger) => {
      return {
        sellPercent: +trigger.sellPercent,
        priceChangePercent: +trigger.priceChangePercent,
      };
    });

    if ((!isPairWithSui(pair) && isOrder) || isShowReceiveSui) {
      if (setReceiveToken) {
        setReceiveToken(isReceiveSui ? SUI_TOKEN_ADDRESS_FULL : null);
      }
    }

    setTriggersOrder(dataFormat);

    if (typeSnipe === "liquidity") {
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeLiquidity: {
          ...snipeSettings?.snipeLiquidity,
          autoSell: {
            ...snipeSettings?.snipeLiquidity.autoSell,
            triggers: dataFormat,
            receiveToken: isShowReceiveSui
              ? isReceiveSui
                ? SUI_TOKEN_ADDRESS_FULL
                : null
              : snipeSettings?.snipeLiquidity?.autoSell?.receiveToken,
          },
        },
      });
    }

    if (typeSnipe === "dex") {
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeDex: {
          ...snipeSettings?.snipeDex,
          autoSell: {
            ...snipeSettings?.snipeDex.autoSell,
            triggers: dataFormat,
            receiveToken: isShowReceiveSui
              ? isReceiveSui
                ? SUI_TOKEN_ADDRESS_FULL
                : null
              : snipeSettings?.snipeDex?.autoSell?.receiveToken,
          },
        },
      });
    }

    if (typeSnipe === "migrationDex") {
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeMigrationDex: {
          ...snipeSettings?.snipeMigrationDex,
          autoSell: {
            ...snipeSettings?.snipeMigrationDex.autoSell,
            triggers: dataFormat,
            receiveToken: null,
          },
        },
      });
    }

    if (isOrder) {
      Storage.setOrderSettings({
        ...orderSettings,
        autoSell: {
          ...orderSettings.autoSell,
          triggers: dataFormat,
          receiveToken: !isPairWithSui(pair)
            ? isReceiveSui
              ? SUI_TOKEN_ADDRESS_FULL
              : null
            : orderSettings?.autoSell?.receiveToken,
        },
      });
    }

    onClose();
  };

  const onSelectReceiveSui = () => {
    setIsReceiveSui(!isReceiveSui);
  };

  return (
    <div className="p-2">
      <div className="body-md-regular-14 text-white-500 mb-4">Auto TP/SL</div>

      <AppButton
        disabled={triggers.length === 5}
        onClick={addTrigger}
        variant="buy"
        size="large"
        className="flex items-center gap-2"
      >
        <PlusIcon />
        Add Order
      </AppButton>

      <div className="body-md-regular-14 text-white-500 mb-6 mt-4 flex gap-4">
        <div>Order #</div>
        <div>Trigger at#</div>
        <div className="ml-[20px]">Sell #</div>
      </div>

      <div className="flex flex-col gap-4">
        {triggers.map((item, index) => {
          return (
            <div
              key={index}
              className="border-white-100 bg-white-50 flex items-center justify-between rounded-[6px] border px-2 py-1"
            >
              <div className="text-white-500 body-md-regular-14 flex gap-1">
                {new BigNumber(item.priceChangePercent).lt(0) ? "SL" : "TP"}{" "}
                {index + 1}
              </div>

              <div className="flex items-center gap-2">
                <div className="bg-black-900 flex items-center gap-2 rounded-[6px] p-2">
                  <NumericFormat
                    placeholder="Trigger"
                    value={item.priceChangePercent ?? ""}
                    allowLeadingZeros
                    thousandSeparator=","
                    className="body-sm-regular-12 placeholder:text-white-300 w-[80px] bg-transparent outline-none"
                    decimalScale={2}
                    onValueChange={({ floatValue }) => {
                      setTriggers(
                        triggers.map((item, indexToUpdate) => {
                          if (indexToUpdate === index) {
                            return {
                              ...item,
                              priceChangePercent: floatValue?.toString(),
                            };
                          }
                          return item;
                        })
                      );
                    }}
                  />
                  <div className="text-white-500 body-md-regular-14">%</div>
                </div>
                <div className="bg-black-900 flex items-center gap-2 rounded-[4px] p-2">
                  <NumericFormat
                    placeholder="Sell"
                    value={item.sellPercent ?? ""}
                    allowLeadingZeros
                    allowNegative={false}
                    thousandSeparator=","
                    className="body-sm-regular-12 placeholder:text-white-300 w-[80px] bg-transparent outline-none"
                    decimalScale={2}
                    onValueChange={({ floatValue }) => {
                      setTriggers(
                        triggers.map((item, indexToUpdate) => {
                          if (indexToUpdate === index) {
                            return {
                              ...item,
                              sellPercent: floatValue?.toString(),
                            };
                          }
                          return item;
                        })
                      );
                    }}
                  />
                  <div className="text-white-500 body-md-regular-14">%</div>
                </div>

                <div
                  className="cursor-pointer text-red-500"
                  onClick={() => {
                    removeTrigger(index);
                  }}
                >
                  <TrashIcon />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {((!isPairWithSui(pair) && isOrder) || isShowReceiveSui) && (
        <div
          className="body-md-regular-14 mt-4 flex w-max cursor-pointer items-center gap-2"
          onClick={onSelectReceiveSui}
        >
          {isReceiveSui ? <CheckedIcon /> : <CheckboxIcon />} Receive SUI
        </div>
      )}

      <AppButton
        onClick={onSave}
        variant="buy"
        size="large"
        className="mx-auto mt-6 w-max"
      >
        Save Order
      </AppButton>
    </div>
  );
};
