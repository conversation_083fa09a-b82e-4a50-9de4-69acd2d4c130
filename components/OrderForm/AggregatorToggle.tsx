import React from "react";
import { AppToggle } from "@/components";
import { TPair } from "@/types";
import { EDex, MEME_DEXES } from "@/enums";

interface AggregatorToggleProps {
  useAggregator: boolean;
  onToggle: (value: boolean) => void;
  disabled?: boolean;
  pair?: TPair;
}

export const AggregatorToggle: React.FC<AggregatorToggleProps> = ({
  useAggregator,
  onToggle,
  disabled = false,
  pair,
}) => {
  const shouldHide =
    pair?.dex?.dex && MEME_DEXES.includes(pair.dex.dex as EDex);

  if (shouldHide) {
    return null;
  }

  return (
    <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
      <AppToggle
        value={useAggregator}
        onChange={() => onToggle(!useAggregator)}
        disabled={disabled}
      />
      <div className="flex items-center gap-1">Aggregator</div>
    </div>
  );
};
