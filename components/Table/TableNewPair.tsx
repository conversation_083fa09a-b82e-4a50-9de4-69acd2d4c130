"use client";

import BigNumber from "bignumber.js";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { DexscreenerIcon, FlashIcon, SearchIcon } from "@/assets/icons";
import { useOrder } from "@/hooks/useOrder";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import Storage from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import { TPair } from "@/types/pair.type";
import { formatNumber } from "@/utils/format";
import {
  filterParams,
  get24hPreviousSeconds,
  getTimeFormatBoots,
  multipliedBN,
} from "@/utils/helper";
import {
  buildTemplateUpdatePairStatsToPairTable,
  buildTemplateUpdateTokenAuditToPairTable,
  buildTemplateUpdateTokenSocialToPairTable,
  buildTemplateUpdateBondingCurveToPairTable,
  filterAuditChecked,
  getCirculatingSupply,
  getClassColor,
  isPairWithSui,
  // isFilterValidPair,
  overrideDataTableFilter,
} from "@/utils/pair";
import { AppCopy } from "../AppCopy";
import { AppDataTableRealtime } from "../AppDataTableRealtime";
import { AppLogoNetwork } from "../AppLogoNetwork";
import { AppNumber, AppNumberUSDCustomDecimals } from "../AppNumber";
import { AppTimeDisplay } from "../AppTimeDisplay";
import { WarningLiquidity } from "../ListPair";
import { BaseToken } from "../Pair/BaseToken";
import { PairFavourite } from "../Pair/Favourite";
import { PairSocials } from "../Pair/Socials";
import { PairIssues } from "../PairIssues";
import { NETWORKS, SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import Tooltip from "rc-tooltip";
import moment from "moment";
import { useExternalWallet } from "@/hooks";
import { toastSuccess } from "@/libs/toast";
import { getLinkTxExplorer } from "@/utils/helper";
import { toastError } from "@/libs/toast";
import { useBalanceOnchain } from "@/hooks";
import { useLogin } from "../../hooks/useLogin";
import { usePrivyTradingEnablement } from "../../hooks/usePrivyTradingEnablement";
import { PAIR_FILTER_STORAGE_KEY } from "@/constants";

const LIMIT_PER_PAGE = 50;

export const TableNewPair = ({
  params,
  buyAmount,
  tableHeight,
}: {
  params: any;
  buyAmount: string;
  tableHeight: number | string;
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const router = useRouter();

  const dataTableRef = useRef<HTMLDivElement>(null);
  const paramsRef = useRef<any>(params);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();

  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });

  const { quickBuy } = useOrder();
  const dispatch = useDispatch<AppDispatch>();
  const { onBuyToken } = useExternalWallet();
  const { balanceSui } = useBalanceOnchain();
  const { onLogin } = useLogin();

  const handleWhenRefreshData = useCallback((event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  }, []);

  useEffect(() => {
    if (!connectedSocket) return;
    subscribeSocketRoom(NETWORKS.SUI, SOCKETS_ROOMS.ALL_PAIR);
    return () => {
      unsubscribeSocketRoom(NETWORKS.SUI, SOCKETS_ROOMS.ALL_PAIR);
    };
  }, [connectedSocket]);

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [handleWhenRefreshData]);

  useEffect(() => {
    Storage.setPairSearch(PAIR_FILTER_STORAGE_KEY, params);
    (dataTableRef.current as any)?.filter(filterParams(params));
    paramsRef.current = params;
  }, [params]);

  const getNewPairs = useCallback(async (dataTableFilter: any) => {
    const res = await rf
      .getRequest("PairRequest")
      .getPairRecently(NETWORKS.SUI, {
        limit: LIMIT_PER_PAGE,
        timestamp: get24hPreviousSeconds(),
        ...overrideDataTableFilter(dataTableFilter),
      });

    let newData = res?.docs || [];
    newData = newData.map((item: TPair) => {
      return {
        ...item,
        priceUsd: item.tokenBase.priceUsd,
        priceSui: item.tokenBase.price,
      };
    });
    newData = filterAuditChecked(newData, dataTableFilter);

    return { data: newData };
  }, []);

  const isNewPair = useCallback(
    (item: TPair) => {
      if (!dataTableRef.current) return false;
      const newData = (dataTableRef?.current as any)?.getNewData();
      return newData.some((newPair: TPair) => newPair.pairId === item.pairId);
    },
    [dataTableRef]
  );

  const TableRow = ({ item, ...restProps }: any) => {
    return (
      <tr
        className={` ${
          isTablet
            ? ``
            : `hover:bg-neutral-alpha-50 border-white-50 cursor-pointer border-b`
        } group  ${isNewPair(item) ? "animate-new-transaction" : ""}`}
        {...restProps}
      />
    );
  };

  const Table = ({ ...restProps }: any) => {
    return <table className="c w-full" {...restProps} />;
  };

  const TableComponents = {
    TableRow,
    Table,
  };

  const PairItem = memo(
    ({
      item,
      index,
    }: {
      item: any;
      index: number;
      isItemInBasicTable?: boolean;
    }) => {
      const quotePrices = useSelector(
        (state: RootState) => state.metadata.quotePrices
      );
      const tokenBasePriceUsd = item.priceUsd || item?.tokenBase?.priceUsd;
      const tokenQuotePriceUsd =
        quotePrices[item.tokenQuote.address]?.priceUsd ||
        item?.tokenQuote?.priceUsd;
      const marketCapUsd = multipliedBN(
        tokenBasePriceUsd,
        getCirculatingSupply(item)
      );

      const onBuySuccess = async (digest?: string) => {
        toastSuccess(
          "Success",
          `You bought ${buyAmount} ${item?.tokenBase?.symbol}`,
          {
            link: getLinkTxExplorer(NETWORKS.SUI, digest || ""),
            text: "View Explorer",
          }
        );
        setIsLoading(false);
      };

      const handleQuickBuy = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        if (isLoading) return;
        if (isExternalWallet) {
          if (!+balanceSui || new BigNumber(buyAmount).gt(balanceSui)) {
            return toastError("Error", "Insufficient Balance");
          }
          onBuyToken(item, buyAmount, setIsLoading, onBuySuccess).then();
          return;
        }

        if (!accessToken) {
          onLogin();
          return;
        }

        if (!wallets.length) {
          dispatch(setIsShowModalAddWallet({ isShow: true }));
          return;
        }

        if (isPrivyUser && !isTradingEnabled) {
          dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
          return;
        }

        quickBuy(
          null,
          item,
          buyAmount,
          isPairWithSui(item) ? "" : SUI_TOKEN_ADDRESS_FULL,
          false
        ).then();
      };

      const isDefaultValueTxns = (value: string | number | BigNumber) => {
        if (!value || new BigNumber(value || 0).isZero()) {
          return true;
        }
        return false;
      };

      const CellItemWrapper = memo(
        ({
          className = "",
          children,
        }: {
          className?: string;
          children: React.ReactNode;
        }) => (
          <td className={className}>
            <Link href={`/${item.network}/${encodeURI(item.slug)}`}>
              {children}
            </Link>
          </td>
        )
      );
      CellItemWrapper.displayName = "CellItemWrapper";

      if (isTablet) {
        return (
          <div className="border-white-50 bg-white-25 body-xs-regular-8 min-h-[50px] rounded-[4px] border p-[8px]">
            <div
              className="flex cursor-pointer items-center gap-[6px]"
              onClick={() =>
                router.push(`/${item.network}/${encodeURI(item.slug)}`)
              }
            >
              <div className="flex gap-1">
                <BaseToken
                  pair={item}
                  size={24}
                  classNameAvatarToken="w-[24px] h-[24px]"
                />
                <div className="min-w-[65px]">
                  <div className="flex flex-col">
                    <div className="body-sm-medium-12 max-w-[65px] truncate">
                      {item?.tokenBase?.symbol || "Unknown"}
                    </div>

                    <div className="flex items-center gap-1">
                      <AppTimeDisplay
                        timestamp={item.timestamp * 1000}
                        isAgo
                        suffix=""
                        classNameWrapper="body-xs-regular-10 !text-brand-500"
                      />
                      <PairSocials
                        info={item?.tokenBase?.socials}
                        classNameWrapper={"flex gap-1"}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="body-xs-regular-8 flex flex-1 flex-col gap-[2px]">
                <div className="grid w-full grid-cols-3 gap-[6px]">
                  <div className="flex min-w-[40px] items-center gap-[2px]">
                    <div className="text-white-800 body-sm-regular-12">
                      <AppNumber
                        value={tokenBasePriceUsd}
                        isForUSD
                        className=" !text-white-800"
                      />
                    </div>
                  </div>
                  <div className="flex min-w-[50px] items-center gap-[2px]">
                    <div className="text-white-500">MC</div>
                    <div className="!text-white-800 body-sm-regular-12">
                      <AppNumberUSDCustomDecimals
                        value={marketCapUsd}
                        className={getClassColor(marketCapUsd)}
                        decimals={1}
                      />
                    </div>
                  </div>
                  <div className="flex min-w-[40px] items-center gap-[2px]">
                    <div className="text-white-500">Liq</div>
                    <div className="!text-white-800 body-sm-regular-12">
                      <AppNumberUSDCustomDecimals
                        value={multipliedBN(tokenQuotePriceUsd, item.liquidity)}
                        className={getClassColor(
                          multipliedBN(tokenQuotePriceUsd, item.liquidity)
                        )}
                        decimals={1}
                      />
                    </div>
                  </div>
                </div>
                <div className="grid w-full grid-cols-3 gap-[6px]">
                  <div className="flex min-w-[40px] items-center gap-[2px]">
                    <div className="text-white-500">Vol</div>
                    <div className="text-white-800 body-sm-regular-12">
                      <AppNumberUSDCustomDecimals
                        value={item.volumeUsd}
                        className={getClassColor(item.volumeUsd)}
                        decimals={1}
                      />
                    </div>
                  </div>
                  <div className="flex min-w-[50px] items-center gap-[2px]">
                    <div className="text-white-500">Txns</div>
                    <div className="text-white-800 body-xs-regular-10">
                      <div className="text-white-500 body-sm-regular-10 flex justify-start gap-[2px]">
                        <span
                          className={`${
                            isDefaultValueTxns(item.buyTxns)
                              ? "text-white-500"
                              : "text-green-500"
                          }`}
                        >
                          {formatNumber(item.buyTxns, 1)}
                        </span>{" "}
                        /{" "}
                        <span
                          className={`${
                            isDefaultValueTxns(item.sellTxns)
                              ? "text-white-500"
                              : "text-red-500"
                          }`}
                        >
                          {formatNumber(item.sellTxns, 1)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex min-w-[40px] items-center gap-[2px]">
                    <div
                      className="text-white-800 w-max"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {item?.slug && (
                        <PairIssues
                          key={item?.slug}
                          isShowFull={false}
                          pair={item}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex h-[24px] justify-center">
                <div
                  onClick={handleQuickBuy}
                  className={`body-xs-medium-10 text-brand-500 border-brand-800 hover:bg-brand-800 flex w-full cursor-pointer items-center justify-center gap-1 rounded-[6px] border px-[8px] py-[4px]`}
                >
                  <span className="max-w-[24px] truncate">
                    {buyAmount || "0"}
                  </span>
                  <AppLogoNetwork network={NETWORKS.SUI} isBase />
                </div>
              </div>
            </div>
          </div>
        );
      }

      return (
        <>
          <CellItemWrapper className="sticky left-0 bg-[#06070e] group-hover:bg-[#13141a] lg:static lg:bg-transparent">
            <div className="td flex justify-center px-[4px] md:px-[8px]">
              <PairFavourite pair={item} />
            </div>
          </CellItemWrapper>
          <CellItemWrapper className="sticky left-[26px] bg-[#06070e] group-hover:bg-[#13141a] md:left-[34px] lg:static lg:bg-transparent">
            <div className="td border-white-50 flex h-[64px] w-[130px] items-center gap-[8px] border-r md:w-auto md:border-0">
              <BaseToken pair={item} />

              <div>
                <div className="flex items-center gap-1">
                  <div className="body-sm-medium-12  max-w-[100px] truncate">
                    {item?.tokenBase?.symbol || "Unknown"}
                  </div>
                  <div onClick={(e) => e.preventDefault()}>
                    <AppCopy
                      message={item?.tokenBase?.address}
                      className="text-white-700 hover:text-white-1000"
                    />
                  </div>

                  <Tooltip
                    overlay={<span>Search on Twitter</span>}
                    placement="bottom"
                    overlayClassName="whitespace-nowrap body-xs-regular-8"
                  >
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        window.open(
                          `https://x.com/search?q=${item?.tokenBase?.address}`,
                          "_blank",
                          "noopener,noreferrer"
                        );
                      }}
                      className="cursor-pointer"
                    >
                      <SearchIcon className="text-white-500 h-[14px] w-[14px]" />
                    </span>
                  </Tooltip>

                  {!!item?.tokenBase?.isDexscreenerVerified && (
                    <Tooltip
                      overlay="DexScreener Social Updated"
                      placement="bottom"
                      overlayClassName="whitespace-nowrap"
                    >
                      <DexscreenerIcon className="text-white-500 h-[14px] w-[14px]" />
                    </Tooltip>
                  )}

                  {item?.tokenBase?.boostFactor &&
                    moment(
                      item?.tokenBase?.isBoostedUntil,
                      "YYYY-MM-DD H:mm:ss.S Z"
                    ).valueOf() > moment().valueOf() && (
                      <div className="body-sm-medium-12 flex items-center text-orange-500">
                        <FlashIcon className="h-4 w-4" />
                        {getTimeFormatBoots(item?.tokenBase?.boostFactor || 0)}
                      </div>
                    )}

                  <WarningLiquidity pair={item} />
                </div>

                <div className="flex items-center gap-1">
                  <PairSocials info={item?.tokenBase?.socials} />
                </div>
              </div>
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td body-sm-regular-12 text-white-500 flex min-w-[67px] justify-center md:min-w-[80px]">
              <AppTimeDisplay
                timestamp={item.timestamp * 1000}
                isAgo
                suffix=""
              />
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td body-sm-regular-12 flex min-w-[80px] justify-end md:min-w-[132px]">
              <AppNumber
                value={multipliedBN(tokenQuotePriceUsd, item.liquidity)}
                isForUSD
                className={getClassColor(
                  multipliedBN(tokenQuotePriceUsd, item.liquidity)
                )}
              />
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td min-w-[90px] flex-col !items-end md:min-w-[132px]">
              <div className="body-sm-regular-12">
                <AppNumber
                  value={marketCapUsd}
                  isForUSD
                  className={getClassColor(marketCapUsd)}
                />
              </div>
              <div>
                <AppNumber
                  value={tokenBasePriceUsd}
                  isForUSD
                  className="body-xs-regular-10 text-white-500"
                />
              </div>
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td min-w-[90px] flex-col !items-end md:min-w-[132px]">
              <div className="body-sm-regular-12">
                <AppNumber value={item?.tokenBase?.holdersCount} />
              </div>
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td min-w-[90px] flex-col !items-end md:min-w-[132px]">
              <div
                className={`body-sm-medium-12 ${
                  isDefaultValueTxns(item.totalTxns)
                    ? "text-white-500"
                    : "text-white-0"
                }`}
              >
                {formatNumber(item.totalTxns, 2)}
              </div>
              <div className="text-white-500 body-sm-regular-12 flex justify-end gap-[2px]">
                <span
                  className={`${
                    isDefaultValueTxns(item.buyTxns)
                      ? "text-white-500"
                      : "text-green-500"
                  }`}
                >
                  {formatNumber(item.buyTxns, 2)}
                </span>{" "}
                /{" "}
                <span
                  className={`${
                    isDefaultValueTxns(item.sellTxns)
                      ? "text-white-500"
                      : "text-red-500"
                  }`}
                >
                  {formatNumber(item.sellTxns, 2)}
                </span>
              </div>
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td body-sm-regular-12 flex min-w-[90px] justify-end md:min-w-[132px]">
              <AppNumber
                value={item.volumeUsd}
                isForUSD
                className={getClassColor(item.volumeUsd)}
              />
            </div>
          </CellItemWrapper>

          <CellItemWrapper>
            <div className="td flex min-w-[296px] justify-center">
              {item?.slug && (
                <PairIssues key={item?.slug} isShowFull pair={item} />
              )}
            </div>
          </CellItemWrapper>

          <CellItemWrapper className="sticky right-0 bg-[#06070e] group-hover:bg-[#13141a] lg:static lg:bg-transparent">
            <div className="td border-white-50 flex h-[64px] justify-center border-l md:border-0">
              <div
                onClick={handleQuickBuy}
                className={`body-sm-medium-12 text-brand-500 bg-brand-800 border-brand-800 flex min-w-[60px] items-center justify-center gap-1 rounded-[6px] border p-[2px] md:min-w-[92px] md:p-[8px]`}
              >
                <FlashIcon />
                {buyAmount || "0"}
                <AppLogoNetwork network={NETWORKS.SUI} isBase />
              </div>
            </div>
          </CellItemWrapper>
        </>
      );
    }
  );
  PairItem.displayName = "PairItem";

  return (
    <>
      <AppDataTableRealtime
        isHideHeader={isTablet}
        minWidth={1440}
        ref={dataTableRef}
        shouldAutoFetchOnInit={false}
        height={tableHeight}
        overrideHeaderClassName={`min-w-[1440px] px-[12px] flex body-sm-regular-12 text-white-500 border-y border-white-50 bg-white-50`}
        overrideBodyClassName={`w-full hide-scroll ${
          isTablet ? "flex flex-col gap-2 mb-2" : ""
        }`}
        getData={getNewPairs}
        limit={LIMIT_PER_PAGE}
        handleAddNewItem={{
          broadcastName: BROADCAST_EVENTS.PAIR_CREATED,
          fieldKey: "pairId",
          formatter: async (data) => {
            const newPair = await rf
              .getRequest("PairRequest")
              .getPair(data.network, data.pairId);

            newPair.timestamp = new Date().getTime() / 1000;

            console.log("[TableNewPair] newPair", {
              newPair,
              paramsRef: paramsRef.current,
              dexes: paramsRef.current?.dexes,
              dex: newPair?.dex?.dex,
              isInclude: paramsRef.current?.dexes?.includes(newPair?.dex?.dex),
            });

            if (
              !paramsRef.current?.dexes ||
              paramsRef.current?.dexes?.includes(newPair?.dex?.dex)
            ) {
              return {
                ...data,
                ...newPair,
              };
            }
            return null;
          },
        }}
        handleUpdateItem={[
          buildTemplateUpdatePairStatsToPairTable(),
          buildTemplateUpdateTokenAuditToPairTable(),
          buildTemplateUpdateTokenSocialToPairTable(),
          buildTemplateUpdateBondingCurveToPairTable(),
        ]}
        renderHeader={() => {
          if (isTablet) return <></>;
          return (
            <>
              <tr className="body-sm-regular-12 text-white-500 h-[31px] bg-[#13141a]">
                <th className="md:min-[32px] sticky left-0 w-[5%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:px-[8px] lg:static lg:bg-transparent" />
                <th className="sticky left-[26px] w-[16%]  min-w-[130px] bg-[#13141a] md:left-[34px] md:min-w-[200px] lg:static lg:bg-transparent ">
                  <div className="border-white-50 border-r px-[8px] py-[6px] text-left md:border-0">
                    Token
                  </div>
                </th>
                <th className="w-[7%] min-w-[67px] px-[8px] py-[6px] text-center md:min-w-[80px]">
                  Created
                </th>
                <th className="w-[10%] min-w-[122px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Liquidity
                </th>
                <th className="w-[10%] min-w-[70px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  MC
                </th>
                <th className="w-[10%] min-w-[70px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Holders
                </th>
                <th className="w-[10%] min-w-[62px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Tnxs
                </th>
                <th className="w-[10%] min-w-[70px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Vol.
                </th>
                <th
                  className={`w-[20%] min-w-[296px] px-[8px] py-[6px] text-center`}
                >
                  Audit
                </th>
                <th className="sticky right-0 w-[12%] min-w-[78px] bg-[#13141a] text-center md:min-w-[148px] lg:static lg:bg-transparent">
                  <div className="border-white-50 w-full border-l px-[8px] py-[6px] text-center md:border-0">
                    Action
                  </div>
                </th>
              </tr>
            </>
          );
        }}
        renderRow={(item: any, index: number) => {
          if (isTablet) {
            return (
              <>
                <div className="flex flex-col gap-2">
                  <PairItem
                    key={`${item.dex?.name}-${item.pairId}`}
                    item={item}
                    index={index}
                    isItemInBasicTable
                  />
                </div>
              </>
            );
          }
          return (
            <>
              <PairItem
                key={`${item.dex?.name}-${item.pairId}`}
                item={item}
                index={index}
                isItemInBasicTable
              />
            </>
          );
        }}
        isBasicTable={!isTablet}
        components={TableComponents}
      />
    </>
  );
};
TableNewPair.displayName = "TableNewPair";
