"use client";

import { useMediaQuery } from "react-responsive";
import { PairTrendingItem } from "../Pair/TrendingItem";
import React, {
  forwardRef,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
import { AppButtonSort } from "../AppButtonSort";
import { AppDataTableRealtime } from "../AppDataTableRealtime";
import { TPair } from "@/types";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import {
  buildTemplateUpdatePairStatsToPairTable,
  buildTemplateUpdateTokenAuditToPairTable,
  buildTemplateUpdateTokenSocialToPairTable,
  filterAuditChecked,
  overridePairStatIfNeed,
} from "@/utils/pair";
import rf from "@/services/RequestFactory";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import { NETWORKS } from "@/utils/contants";
const LIMIT_PER_PAGE = 200;

type TTableFavouritePairProps = {
  ref?: React.RefObject<HTMLDivElement>;
  tableHeight: string | number;
  buyAmount?: string;
  customRenderRow?: any;
  customRenderHeader?: any;
};

export const TableFavouritePair = forwardRef(function TableFavouritePair(
  props: TTableFavouritePairProps,
  ref: React.ForwardedRef<HTMLDivElement>
) {
  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });
  const dataTableRef = useRef<HTMLDivElement>(null);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );

  useEffect(() => {
    if (!sortBy || !sortType) return;
    (dataTableRef.current as any)?.filter({
      sortBy,
      sortType,
    });
  }, [sortBy, sortType]);

  const handleWhenSocketConnected = useCallback(() => {
    const items = (dataTableRef.current as any)?.getItems();
    items?.forEach((item: TPair) => {
      subscribeSocketRoom(NETWORKS.SUI, SOCKETS_ROOMS.PAIR_DETAIL(item.pairId));
    });
  }, []);

  useEffect(() => {
    if (!connectedSocket) return;
    handleWhenSocketConnected();
    return () => {
      const items = (dataTableRef.current as any)?.getItems();
      items?.forEach((item: TPair) => {
        unsubscribeSocketRoom(
          NETWORKS.SUI,
          SOCKETS_ROOMS.PAIR_DETAIL(item.pairId)
        );
      });
    };
  }, [connectedSocket]);

  useEffect(() => {
    const tableRef = ref || dataTableRef;
    const handleWhenAddedPair = (event: TBroadcastEvent) => {
      const { pair } = event.detail;
      if (tableRef && "current" in tableRef) {
        (tableRef.current as any)?.appendNewData(pair);
      }
      subscribeSocketRoom(NETWORKS.SUI, SOCKETS_ROOMS.PAIR_DETAIL(pair.pairId));
    };

    const handleWhenRemovedPair = (event: TBroadcastEvent) => {
      const { pair } = event.detail;
      if (tableRef && "current" in tableRef) {
        (tableRef.current as any)?.removeItem("slug", pair?.slug);
      }
      unsubscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.PAIR_DETAIL(pair.pairId)
      );
    };

    AppBroadcast.on(
      BROADCAST_EVENTS.REMOVED_FAVOURITE_PAIR,
      handleWhenRemovedPair
    );

    AppBroadcast.on(BROADCAST_EVENTS.ADDED_FAVOURITE_PAIR, handleWhenAddedPair);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ADDED_FAVOURITE_PAIR,
        handleWhenAddedPair
      );

      AppBroadcast.remove(
        BROADCAST_EVENTS.REMOVED_FAVOURITE_PAIR,
        handleWhenRemovedPair
      );
    };
  }, []);

  const applySort = (pairs: TPair[], sortBy: string, sortType: string) => {
    switch (sortBy) {
      case "timestamp":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? a.timestamp - b.timestamp
            : b.timestamp - a.timestamp;
        });
      case "liquidity":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.liquidity) - Number(a.liquidity)
            : Number(a.liquidity) - Number(b.liquidity);
        });
      case "mc":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.marketCapUsd) - Number(a.marketCapUsd)
            : Number(a.marketCapUsd) - Number(b.marketCapUsd);
        });
      case "tnxs":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.sellTxn["24h"]) - Number(a.stats?.sellTxn["24h"])
            : Number(a.stats?.sellTxn["24h"]) - Number(b.stats?.sellTxn["24h"]);
        });
      case "vol":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.volume["24h"]) - Number(a.stats?.volume["24h"])
            : Number(a.stats?.volume["24h"]) - Number(b.stats?.volume["24h"]);
        });
      case "price":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.priceUsd) - Number(a.priceUsd)
            : Number(a.priceUsd) - Number(b.priceUsd);
        });
      case "percent_5m":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.percent["5m"]) - Number(a.stats?.percent["5m"])
            : Number(a.stats?.percent["5m"]) - Number(b.stats?.percent["5m"]);
        });
      case "percent_1h":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.percent["1h"]) - Number(a.stats?.percent["1h"])
            : Number(a.stats?.percent["1h"]) - Number(b.stats?.percent["1h"]);
        });
      case "percent_6h":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.percent["6h"]) - Number(a.stats?.percent["6h"])
            : Number(a.stats?.percent["6h"]) - Number(b.stats?.percent["6h"]);
        });
      case "percent_24h":
        return pairs.sort((a: TPair, b: TPair) => {
          return sortType === "desc"
            ? Number(b.stats?.percent["24h"]) - Number(a.stats?.percent["24h"])
            : Number(a.stats?.percent["24h"]) - Number(b.stats?.percent["24h"]);
        });
      default:
        return pairs;
    }
  };

  const applySearch = (pairs: TPair[], search: string) => {
    if (!search) {
      return pairs;
    }

    return pairs.filter((item: TPair) => {
      return item.tokenBase.symbol.toLowerCase().includes(search.toLowerCase());
    });
  };

  const getPairs = async (dataTableFilter: any) => {
    if (!accessToken) {
      return {
        data: [],
      };
    }
    const res = await rf.getRequest("FavouriteRequest").getPairs(NETWORKS.SUI, {
      page: 1,
      ...dataTableFilter,
      limit: LIMIT_PER_PAGE,
    });
    let newData = res?.docs || [];
    newData = newData.map((item: TPair) => {
      return {
        ...item,
        stats: overridePairStatIfNeed(item.stats),
        priceUsd: item.tokenBase.priceUsd,
        priceSui: item.tokenBase.price,
      };
    });
    newData = applySort(
      newData,
      dataTableFilter.sortBy,
      dataTableFilter.sortType
    );
    newData = filterAuditChecked(newData, dataTableFilter);
    if (dataTableFilter?.search !== undefined) {
      newData = applySearch(newData, dataTableFilter.search);
    }

    // emit event to socket
    newData?.forEach((item: TPair) => {
      subscribeSocketRoom(NETWORKS.SUI, SOCKETS_ROOMS.PAIR_DETAIL(item.pairId));
    });

    return {
      data: newData,
    };
  };

  const TableRow = ({ item, ...restProps }: any) => {
    return (
      <tr
        className={`hover:bg-neutral-alpha-50 border-white-50 group  cursor-pointer border-b`}
        {...restProps}
      />
    );
  };

  const Table = ({ ...restProps }: any) => {
    return <table className="w-full" {...restProps} />;
  };

  const TableComponents = {
    TableRow,
    Table,
  };

  const renderHeader = () => {
    return (
      <>
        {!isTablet && (
          <tr className="body-sm-regular-12 text-white-500 h-[31px] bg-[#13141a]">
            <th className="md:min-[32px] sticky left-0 w-[3%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:px-[8px] lg:static lg:bg-transparent" />

            <th className="sticky left-[25px] w-[13%]  min-w-[124px] bg-[#13141a] md:left-[30px] md:min-w-[200px] lg:static lg:bg-transparent ">
              <div className="border-white-50 border-r px-[8px] py-[6px] text-left md:border-0">
                Pair Info
              </div>
            </th>
            <th className="w-[7%] min-w-[92px] px-[8px] py-[6px] text-right md:min-w-[132px]">
              <div className="flex items-center justify-end gap-1">
                Liquidity
                <AppButtonSort
                  value="liquidity"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                MC
                <AppButtonSort
                  value="mc"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">Holders</div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                Tnxs
                <AppButtonSort
                  value="tnxs"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                Vol.
                <AppButtonSort
                  value="vol"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                5m
                <AppButtonSort
                  value="percent_5m"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                1h
                <AppButtonSort
                  value="percent_1h"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                6h
                <AppButtonSort
                  value="percent_6h"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              <div className="flex items-center justify-end gap-1">
                24h
                <AppButtonSort
                  value="percent_24h"
                  sortBy={sortBy}
                  sortType={sortType}
                  setSortType={setSortType}
                  setSortBy={setSortBy}
                />
              </div>
            </th>
            <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
              DEV
            </th>
            <th className={`w-[8%] min-w-[105px] px-[8px] py-[6px]`}>
              Audit check
            </th>
            <th className="sticky right-0 w-[11%] min-w-[78px] bg-[#13141a] text-center md:min-w-[148px] lg:static lg:bg-transparent">
              <div className="border-white-50 w-full border-l px-[8px] py-[6px] text-center md:border-0">
                Action
              </div>
            </th>
          </tr>
        )}
      </>
    );
  };

  const renderRow = (item: TPair) => {
    if (isTablet) {
      <div className="mx-2 mb-2 flex flex-col gap-2">
        <PairTrendingItem
          key={`${item.dex?.name}-${item.pairId}`}
          item={item}
          buyAmount={props.buyAmount || "1"}
          isItemInBasicTable
          isFavorite
        />
      </div>;
    }

    return (
      <PairTrendingItem
        key={`${item.dex?.name}-${item.pairId}`}
        item={item}
        buyAmount={props.buyAmount || "1"}
        isItemInBasicTable
        isFavorite
      />
    );
  };

  return (
    <>
      <AppDataTableRealtime
        minWidth={1440}
        ref={ref || dataTableRef}
        height={props.tableHeight}
        overrideHeaderClassName={`min-w-[1440px] px-[12px] flex body-sm-regular-11 text-white-500 border-y border-white-50 bg-white-50`}
        overrideBodyClassName="w-full"
        getData={getPairs}
        limit={LIMIT_PER_PAGE}
        handleUpdateItem={[
          buildTemplateUpdatePairStatsToPairTable(),
          buildTemplateUpdateTokenAuditToPairTable(),
          buildTemplateUpdateTokenSocialToPairTable(),
        ]}
        renderHeader={props.customRenderHeader || renderHeader}
        renderRow={props.customRenderRow || renderRow}
        isBasicTable
        components={TableComponents}
      />
    </>
  );
});
