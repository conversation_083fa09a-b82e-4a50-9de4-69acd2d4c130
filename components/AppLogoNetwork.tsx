import { SuiIcon, SuiIconBase } from "@/assets/icons";
import React from "react";
import { NETWORKS } from "@/utils/contants";

export const AppLogoNetwork = ({
  network,
  className,
  isBase = false,
}: {
  network: string;
  className?: string;
  isBase?: boolean;
}) => {
  if (network === NETWORKS.SUI) {
    if (isBase) {
      return <SuiIconBase className={className} />;
    }
    return <SuiIcon className={className} />;
  }

  return <div />;
};
