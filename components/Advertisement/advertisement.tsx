"use client";

import { toastError } from "@/libs/toast";
import RequestFactory from "@/services/RequestFactory";
import { TTokenAdvertisement } from "@/types";
import { NETWORKS } from "@/utils/contants";
import React, { memo, useCallback, useEffect, useState } from "react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { AdsItem } from "./ads-item";

export const Advertisement: React.FC = memo(function Advertisement() {
  const [advertisingTokens, setAdvertisingTokens] = useState<
    TTokenAdvertisement[]
  >([]);
  const [isHideAd, setIsHideAd] = useState<boolean>(false);

  const getActiveAdvertiseTokens = async () => {
    try {
      const advertisingTokens: TTokenAdvertisement[] =
        await RequestFactory.getRequest(
          "TokenAdvertisementRequest"
        ).getTokenAdvertisement(NETWORKS.SUI);
      if (advertisingTokens.length === 0) {
        const defaultAdvertisingTokens = await getDefaultAdvertiseTokens();
        setAdvertisingTokens(defaultAdvertisingTokens);
      } else {
        setAdvertisingTokens(advertisingTokens);
      }
    } catch (error: any) {
      toastError(
        "Failed to get advertising token",
        error.message || "Unknown error"
      );
    }
  };

  const getDefaultAdvertiseTokens = async () => {
    return [
      {
        network: "sui",
        tokenAddress:
          "0x16ab6a14d76a90328a6b04f06b0a0ce952847017023624e0c37bf8aa314c39ba::shr::SHR",
        logoImageUrl:
          "https://storage.googleapis.com/raidenx-prod/logo/sui/0x16ab6a14d76a90328a6b04f06b0a0ce952847017023624e0c37bf8aa314c39ba::shr::SHR.png",
        socials: {
          websites: [
            {
              label: "Website",
              url: "https://suidaos.com",
            },
          ],
          socials: [
            {
              type: "twitter",
              url: "https://x.com/sroomaidao",
            },
            {
              type: "telegram",
              url: "https://t.me/suidaos",
            },
          ],
        },
        title: "SHR0",
        pitch: "MoonBags token to the moon",
        pairSlug: "cetus-shr0-sui-236113",
      },
    ];
  };

  useEffect(() => {
    getActiveAdvertiseTokens();
  }, []);

  const handleHideAd = useCallback(() => {
    setIsHideAd(true);
  }, []);

  if (advertisingTokens.length === 0 || isHideAd) return null;

  return (
    <div className="m-2">
      <Swiper
        direction={"vertical"}
        spaceBetween={0}
        centeredSlides={true}
        autoplay={{
          delay: 10000,
          disableOnInteraction: false,
        }}
        navigation={true}
        modules={[Autoplay, Pagination, Navigation]}
        className="h-[147.2px] w-full overflow-hidden"
      >
        {advertisingTokens.map((item) => (
          <SwiperSlide key={item.tokenAddress}>
            <AdsItem item={item} handleHideAd={handleHideAd} />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
});
